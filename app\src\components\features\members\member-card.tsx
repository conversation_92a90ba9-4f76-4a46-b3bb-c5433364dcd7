import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Edit, CreditCard, Smartphone, Eye, Zap } from 'lucide-react'
import { Member } from '@/types'
import { formatDate, getDaysRemaining, getMembershipStatus } from '@/lib/utils'

interface MemberCardProps {
  member: Member
  onEdit?: (member: Member) => void
  onPayment?: (member: Member) => void
  onCheckIn?: (member: Member) => void
  onViewProfile?: (member: Member) => void
}

export function MemberCard({ 
  member, 
  onEdit, 
  onPayment, 
  onCheckIn, 
  onViewProfile 
}: MemberCardProps) {
  const status = getMembershipStatus(member.expiryDate)
  const daysRemaining = getDaysRemaining(member.expiryDate)
  
  const getStatusBadge = () => {
    switch (status) {
      case 'active':
        return <Badge variant="success">🟢 Aktif</Badge>
      case 'expires-soon':
        return <Badge variant="warning">🟡 Segera <PERSON></Badge>
      case 'expired':
        return <Badge variant="destructive">🔴 Kedaluwarsa</Badge>
    }
  }

  const getStatusText = () => {
    if (status === 'expired') {
      return `Kedaluwarsa: ${formatDate(member.expiryDate)} (${Math.abs(daysRemaining)} hari lalu)`
    }
    return `Berakhir: ${formatDate(member.expiryDate)} (${daysRemaining} hari tersisa)`
  }

  const getLastVisitText = () => {
    if (!member.lastVisit) return 'Belum pernah berkunjung'

    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - member.lastVisit.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) return 'Kunjungan terakhir: Baru saja'
    if (diffInHours < 24) return `Kunjungan terakhir: ${diffInHours} jam lalu`

    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays === 0) return 'Kunjungan terakhir: Hari ini'
    if (diffInDays === 1) return 'Kunjungan terakhir: Kemarin'
    if (diffInDays < 7) return `Kunjungan terakhir: ${diffInDays} hari lalu`

    return `Kunjungan terakhir: ${formatDate(member.lastVisit)}`
  }

  const getPackageDisplayName = () => {
    switch (member.packageType) {
      case 'daily':
        return 'Paket Harian'
      case 'weekly':
        return 'Paket Mingguan'
      case 'monthly-basic':
        return 'Paket Bulanan Basic'
      case 'monthly-unlimited':
        return 'Paket Bulanan Unlimited'
      case 'quarterly':
        return 'Paket Triwulan'
      default:
        return member.packageType
    }
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          {/* Member Info */}
          <div className="flex items-start space-x-3 flex-1">
            {/* Avatar */}
            <div className="w-12 h-12 bg-muted rounded-full flex items-center justify-center flex-shrink-0">
              {member.photoURL ? (
                <img 
                  src={member.photoURL} 
                  alt={member.fullName}
                  className="w-12 h-12 rounded-full object-cover"
                />
              ) : (
                <span className="text-lg font-semibold text-muted-foreground">
                  📷
                </span>
              )}
            </div>

            {/* Details */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-1">
                <span className="font-medium text-sm text-muted-foreground">
                  {member.memberId}
                </span>
                <span className="font-semibold">{member.fullName}</span>
                <span className="text-sm text-muted-foreground">
                  📱 {member.phoneNumber}
                </span>
                {getStatusBadge()}
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 text-yellow-500 hover:text-yellow-600"
                  onClick={() => onCheckIn?.(member)}
                  title="Check-in Cepat"
                >
                  <Zap className="h-4 w-4" />
                </Button>
              </div>
              
              <div className="text-sm text-muted-foreground space-y-1">
                <div>
                  {getPackageDisplayName()} • {getStatusText()}
                </div>
                <div>
                  {getLastVisitText()}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center space-x-2 mt-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onEdit?.(member)}
          >
            <Edit className="h-3 w-3 mr-1" />
            Edit
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => onPayment?.(member)}
          >
            <CreditCard className="h-3 w-3 mr-1" />
            {status === 'expired' ? 'Perpanjang' : 'Pembayaran'}
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => onCheckIn?.(member)}
            disabled={status === 'expired'}
          >
            <Smartphone className="h-3 w-3 mr-1" />
            Check-in
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => onViewProfile?.(member)}
          >
            <Eye className="h-3 w-3 mr-1" />
            Lihat Profil
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
