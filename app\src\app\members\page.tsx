'use client'

import { useState, useMemo } from 'react'
import { Search, Filter, RefreshCw, UserPlus } from 'lucide-react'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { MainLayout } from '@/components/layout/main-layout'
import { MemberCard } from '@/components/features/members/member-card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select } from '@/components/ui/select'
import { useAuth } from '@/hooks/useAuth'
import { mockMembers } from '@/data/mockData'
import { Member } from '@/types'
import { getMembershipStatus } from '@/lib/utils'

export default function MembersPage() {
  const { user } = useAuth()
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [packageFilter, setPackageFilter] = useState('all')
  const [sortBy, setSortBy] = useState('lastVisit')

  // Filter and sort members
  const filteredMembers = useMemo(() => {
    let filtered = mockMembers.filter(member => {
      // Search filter
      const matchesSearch = searchQuery === '' || 
        member.fullName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        member.phoneNumber.includes(searchQuery) ||
        member.memberId.toLowerCase().includes(searchQuery.toLowerCase())

      // Status filter
      const memberStatus = getMembershipStatus(member.expiryDate)
      const matchesStatus = statusFilter === 'all' || memberStatus === statusFilter

      // Package filter
      const matchesPackage = packageFilter === 'all' || member.packageType === packageFilter

      return matchesSearch && matchesStatus && matchesPackage
    })

    // Sort members
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.fullName.localeCompare(b.fullName)
        case 'memberId':
          return a.memberId.localeCompare(b.memberId)
        case 'expiryDate':
          return a.expiryDate.getTime() - b.expiryDate.getTime()
        case 'lastVisit':
        default:
          if (!a.lastVisit && !b.lastVisit) return 0
          if (!a.lastVisit) return 1
          if (!b.lastVisit) return -1
          return b.lastVisit.getTime() - a.lastVisit.getTime()
      }
    })

    return filtered
  }, [searchQuery, statusFilter, packageFilter, sortBy])

  const handleEdit = (member: Member) => {
    console.log('Edit member:', member)
    // TODO: Navigate to edit page or open modal
  }

  const handlePayment = (member: Member) => {
    console.log('Record payment for:', member)
    // TODO: Navigate to payment page or open modal
  }

  const handleCheckIn = (member: Member) => {
    console.log('Check in member:', member)
    // TODO: Process check-in
  }

  const handleViewProfile = (member: Member) => {
    console.log('View profile:', member)
    // TODO: Navigate to profile page
  }

  const getStatusCounts = () => {
    const active = mockMembers.filter(m => getMembershipStatus(m.expiryDate) === 'active').length
    const expiresSoon = mockMembers.filter(m => getMembershipStatus(m.expiryDate) === 'expires-soon').length
    const expired = mockMembers.filter(m => getMembershipStatus(m.expiryDate) === 'expired').length
    return { active, expiresSoon, expired }
  }

  const statusCounts = getStatusCounts()

  return (
    <ProtectedRoute>
      <MainLayout user={user}>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold flex items-center">
                👥 Semua Anggota ({filteredMembers.length})
              </h1>
              <div className="flex items-center space-x-4 mt-2 text-sm text-muted-foreground">
                <span>🟢 Aktif: {statusCounts.active}</span>
                <span>🟡 Segera Berakhir: {statusCounts.expiresSoon}</span>
                <span>🔴 Kedaluwarsa: {statusCounts.expired}</span>
              </div>
            </div>
            <Button>
              <UserPlus className="h-4 w-4 mr-2" />
              Tambah Anggota Baru
            </Button>
          </div>

          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Cari berdasarkan nama atau telepon..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Filters */}
            <div className="flex items-center space-x-2">
              <Select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value="all">Semua Status</option>
                <option value="active">Aktif</option>
                <option value="expires-soon">Segera Berakhir</option>
                <option value="expired">Kedaluwarsa</option>
              </Select>

              <Select
                value={packageFilter}
                onChange={(e) => setPackageFilter(e.target.value)}
              >
                <option value="all">Semua Paket</option>
                <option value="daily">Harian</option>
                <option value="weekly">Mingguan</option>
                <option value="monthly-basic">Bulanan Basic</option>
                <option value="monthly-unlimited">Bulanan Unlimited</option>
                <option value="quarterly">Triwulan</option>
              </Select>

              <Select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
              >
                <option value="lastVisit">Urutkan: Kunjungan Terakhir</option>
                <option value="name">Urutkan: Nama</option>
                <option value="memberId">Urutkan: ID Anggota</option>
                <option value="expiryDate">Urutkan: Tanggal Berakhir</option>
              </Select>

              <Button variant="outline" size="icon">
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Members List */}
          <div className="space-y-4">
            {filteredMembers.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-muted-foreground">Tidak ada anggota yang sesuai dengan kriteria Anda.</p>
                <Button className="mt-4">
                  <UserPlus className="h-4 w-4 mr-2" />
                  Tambah Anggota Pertama
                </Button>
              </div>
            ) : (
              filteredMembers.map((member) => (
                <MemberCard
                  key={member.id}
                  member={member}
                  onEdit={handleEdit}
                  onPayment={handlePayment}
                  onCheckIn={handleCheckIn}
                  onViewProfile={handleViewProfile}
                />
              ))
            )}
          </div>

          {/* Pagination */}
          {filteredMembers.length > 10 && (
            <div className="flex items-center justify-center space-x-2">
              <Button variant="outline" size="sm">
                ← Sebelumnya
              </Button>
              <span className="text-sm text-muted-foreground">
                Halaman 1 dari {Math.ceil(filteredMembers.length / 10)}
              </span>
              <Button variant="outline" size="sm">
                Selanjutnya →
              </Button>
            </div>
          )}
        </div>
      </MainLayout>
    </ProtectedRoute>
  )
}
