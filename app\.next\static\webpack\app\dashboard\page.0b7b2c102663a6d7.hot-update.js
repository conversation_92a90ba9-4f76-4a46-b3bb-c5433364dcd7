"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/features/dashboard/recent-activities.tsx":
/*!*****************************************************************!*\
  !*** ./src/components/features/dashboard/recent-activities.tsx ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecentActivities: () => (/* binding */ RecentActivities),\n/* harmony export */   createActivitiesFromData: () => (/* binding */ createActivitiesFromData)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_Smartphone_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,Smartphone,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_Smartphone_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,Smartphone,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_Smartphone_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,Smartphone,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\n\n\n\nfunction RecentActivities(param) {\n    let { activities, maxItems = 5 } = param;\n    const getActivityIcon = (type)=>{\n        switch(type){\n            case 'checkin':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_Smartphone_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\dashboard\\\\recent-activities.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 16\n                }, this);\n            case 'payment':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_Smartphone_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\dashboard\\\\recent-activities.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 16\n                }, this);\n            case 'registration':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_Smartphone_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 text-purple-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\dashboard\\\\recent-activities.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getActivityBadge = (type)=>{\n        switch(type){\n            case 'checkin':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                    variant: \"default\",\n                    className: \"bg-blue-100 text-blue-800\",\n                    children: \"Check-in\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\dashboard\\\\recent-activities.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 16\n                }, this);\n            case 'payment':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                    variant: \"success\",\n                    children: \"Pembayaran\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\dashboard\\\\recent-activities.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 16\n                }, this);\n            case 'registration':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                    variant: \"secondary\",\n                    children: \"Anggota Baru\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\dashboard\\\\recent-activities.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const formatActivityText = (activity)=>{\n        const timeAgo = getTimeAgo(activity.timestamp);\n        switch(activity.type){\n            case 'checkin':\n                return \"\".concat(activity.memberName, \" melakukan check-in - \").concat(timeAgo);\n            case 'payment':\n                return \"Pembayaran diterima dari \".concat(activity.memberName, \" - \").concat(timeAgo).concat(activity.amount ? \" (\".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(activity.amount), \")\") : '');\n            case 'registration':\n                return \"Anggota baru terdaftar: \".concat(activity.memberName, \" - \").concat(timeAgo);\n        }\n    };\n    const getTimeAgo = (timestamp)=>{\n        const now = new Date();\n        const diffInMinutes = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60));\n        if (diffInMinutes < 1) return 'baru saja';\n        if (diffInMinutes < 60) return \"\".concat(diffInMinutes, \" menit lalu\");\n        const diffInHours = Math.floor(diffInMinutes / 60);\n        if (diffInHours < 24) return \"\".concat(diffInHours, \" jam lalu\");\n        const diffInDays = Math.floor(diffInHours / 24);\n        return \"\".concat(diffInDays, \" hari lalu\");\n    };\n    const displayedActivities = activities.slice(0, maxItems);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                    className: \"text-lg font-semibold\",\n                    children: \"Aktivitas Terbaru\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\dashboard\\\\recent-activities.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\dashboard\\\\recent-activities.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: displayedActivities.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-muted-foreground text-center py-4\",\n                            children: \"Tidak ada aktivitas terbaru\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\dashboard\\\\recent-activities.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 13\n                        }, this) : displayedActivities.map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 mt-0.5\",\n                                        children: getActivityIcon(activity.type)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\dashboard\\\\recent-activities.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-foreground\",\n                                                children: formatActivityText(activity)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\dashboard\\\\recent-activities.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 19\n                                            }, this),\n                                            activity.details && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground mt-1\",\n                                                children: activity.details\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\dashboard\\\\recent-activities.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\dashboard\\\\recent-activities.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: getActivityBadge(activity.type)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\dashboard\\\\recent-activities.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, activity.id, true, {\n                                fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\dashboard\\\\recent-activities.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\dashboard\\\\recent-activities.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    activities.length > maxItems && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            children: \"View All Activities\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\dashboard\\\\recent-activities.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\dashboard\\\\recent-activities.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\dashboard\\\\recent-activities.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\dashboard\\\\recent-activities.tsx\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, this);\n}\n_c = RecentActivities;\n// Helper function to convert visits and payments to activities\nfunction createActivitiesFromData(visits, payments) {\n    const activities = [];\n    // Add check-ins\n    visits.forEach((visit)=>{\n        activities.push({\n            id: \"visit-\".concat(visit.id),\n            type: 'checkin',\n            memberName: visit.memberName,\n            timestamp: visit.checkInTime\n        });\n    });\n    // Add payments\n    payments.forEach((payment)=>{\n        activities.push({\n            id: \"payment-\".concat(payment.id),\n            type: 'payment',\n            memberName: payment.memberName,\n            timestamp: payment.paymentDate,\n            amount: payment.amount,\n            details: payment.notes\n        });\n    });\n    // Sort by timestamp (most recent first)\n    return activities.sort((a, b)=>b.timestamp.getTime() - a.timestamp.getTime());\n}\nvar _c;\n$RefreshReg$(_c, \"RecentActivities\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2ZlYXR1cmVzL2Rhc2hib2FyZC9yZWNlbnQtYWN0aXZpdGllcy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBK0U7QUFDaEM7QUFDRjtBQUNrQjtBQUNIO0FBaUJyRCxTQUFTVSxpQkFBaUIsS0FBbUQ7UUFBbkQsRUFBRUMsVUFBVSxFQUFFQyxXQUFXLENBQUMsRUFBeUIsR0FBbkQ7SUFDL0IsTUFBTUMsa0JBQWtCLENBQUNDO1FBQ3ZCLE9BQVFBO1lBQ04sS0FBSztnQkFDSCxxQkFBTyw4REFBQ1IsMEdBQVVBO29CQUFDUyxXQUFVOzs7Ozs7WUFDL0IsS0FBSztnQkFDSCxxQkFBTyw4REFBQ1IsMEdBQVVBO29CQUFDUSxXQUFVOzs7Ozs7WUFDL0IsS0FBSztnQkFDSCxxQkFBTyw4REFBQ1AsMEdBQVFBO29CQUFDTyxXQUFVOzs7Ozs7UUFDL0I7SUFDRjtJQUVBLE1BQU1DLG1CQUFtQixDQUFDRjtRQUN4QixPQUFRQTtZQUNOLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUNULHVEQUFLQTtvQkFBQ1ksU0FBUTtvQkFBVUYsV0FBVTs4QkFBNEI7Ozs7OztZQUN4RSxLQUFLO2dCQUNILHFCQUFPLDhEQUFDVix1REFBS0E7b0JBQUNZLFNBQVE7OEJBQVU7Ozs7OztZQUNsQyxLQUFLO2dCQUNILHFCQUFPLDhEQUFDWix1REFBS0E7b0JBQUNZLFNBQVE7OEJBQVk7Ozs7OztRQUN0QztJQUNGO0lBRUEsTUFBTUMscUJBQXFCLENBQUNDO1FBQzFCLE1BQU1DLFVBQVVDLFdBQVdGLFNBQVNHLFNBQVM7UUFFN0MsT0FBUUgsU0FBU0wsSUFBSTtZQUNuQixLQUFLO2dCQUNILE9BQU8sR0FBK0NNLE9BQTVDRCxTQUFTSSxVQUFVLEVBQUMsMEJBQWdDLE9BQVJIO1lBQ3hELEtBQUs7Z0JBQ0gsT0FBTyw0QkFBcURBLE9BQXpCRCxTQUFTSSxVQUFVLEVBQUMsT0FBZUosT0FBVkMsU0FBeUUsT0FBL0RELFNBQVNLLE1BQU0sR0FBRyxLQUFxQyxPQUFoQ2YsMERBQWNBLENBQUNVLFNBQVNLLE1BQU0sR0FBRSxPQUFLO1lBQ3BJLEtBQUs7Z0JBQ0gsT0FBTywyQkFBb0RKLE9BQXpCRCxTQUFTSSxVQUFVLEVBQUMsT0FBYSxPQUFSSDtRQUMvRDtJQUNGO0lBRUEsTUFBTUMsYUFBYSxDQUFDQztRQUNsQixNQUFNRyxNQUFNLElBQUlDO1FBQ2hCLE1BQU1DLGdCQUFnQkMsS0FBS0MsS0FBSyxDQUFDLENBQUNKLElBQUlLLE9BQU8sS0FBS1IsVUFBVVEsT0FBTyxFQUFDLElBQU0sUUFBTyxFQUFDO1FBRWxGLElBQUlILGdCQUFnQixHQUFHLE9BQU87UUFDOUIsSUFBSUEsZ0JBQWdCLElBQUksT0FBTyxHQUFpQixPQUFkQSxlQUFjO1FBRWhELE1BQU1JLGNBQWNILEtBQUtDLEtBQUssQ0FBQ0YsZ0JBQWdCO1FBQy9DLElBQUlJLGNBQWMsSUFBSSxPQUFPLEdBQWUsT0FBWkEsYUFBWTtRQUU1QyxNQUFNQyxhQUFhSixLQUFLQyxLQUFLLENBQUNFLGNBQWM7UUFDNUMsT0FBTyxHQUFjLE9BQVhDLFlBQVc7SUFDdkI7SUFFQSxNQUFNQyxzQkFBc0J0QixXQUFXdUIsS0FBSyxDQUFDLEdBQUd0QjtJQUVoRCxxQkFDRSw4REFBQ1oscURBQUlBOzswQkFDSCw4REFBQ0UsMkRBQVVBOzBCQUNULDRFQUFDQywwREFBU0E7b0JBQUNZLFdBQVU7OEJBQXdCOzs7Ozs7Ozs7OzswQkFFL0MsOERBQUNkLDREQUFXQTs7a0NBQ1YsOERBQUNrQzt3QkFBSXBCLFdBQVU7a0NBQ1prQixvQkFBb0JHLE1BQU0sS0FBSyxrQkFDOUIsOERBQUNDOzRCQUFFdEIsV0FBVTtzQ0FBaUQ7Ozs7O21DQUk5RGtCLG9CQUFvQkssR0FBRyxDQUFDLENBQUNuQix5QkFDdkIsOERBQUNnQjtnQ0FBc0JwQixXQUFVOztrREFDL0IsOERBQUNvQjt3Q0FBSXBCLFdBQVU7a0RBQ1pGLGdCQUFnQk0sU0FBU0wsSUFBSTs7Ozs7O2tEQUVoQyw4REFBQ3FCO3dDQUFJcEIsV0FBVTs7MERBQ2IsOERBQUNzQjtnREFBRXRCLFdBQVU7MERBQ1ZHLG1CQUFtQkM7Ozs7Ozs0Q0FFckJBLFNBQVNvQixPQUFPLGtCQUNmLDhEQUFDRjtnREFBRXRCLFdBQVU7MERBQ1ZJLFNBQVNvQixPQUFPOzs7Ozs7Ozs7Ozs7a0RBSXZCLDhEQUFDSjt3Q0FBSXBCLFdBQVU7a0RBQ1pDLGlCQUFpQkcsU0FBU0wsSUFBSTs7Ozs7OzsrQkFmekJLLFNBQVNxQixFQUFFOzs7Ozs7Ozs7O29CQXNCMUI3QixXQUFXeUIsTUFBTSxHQUFHeEIsMEJBQ25CLDhEQUFDdUI7d0JBQUlwQixXQUFVO2tDQUNiLDRFQUFDWCx5REFBTUE7NEJBQUNhLFNBQVE7NEJBQVV3QixNQUFLO3NDQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVFoRDtLQWpHZ0IvQjtBQW1HaEIsK0RBQStEO0FBQ3hELFNBQVNnQyx5QkFDZEMsTUFBZSxFQUNmQyxRQUFtQjtJQUVuQixNQUFNakMsYUFBeUIsRUFBRTtJQUVqQyxnQkFBZ0I7SUFDaEJnQyxPQUFPRSxPQUFPLENBQUNDLENBQUFBO1FBQ2JuQyxXQUFXb0MsSUFBSSxDQUFDO1lBQ2RQLElBQUksU0FBa0IsT0FBVE0sTUFBTU4sRUFBRTtZQUNyQjFCLE1BQU07WUFDTlMsWUFBWXVCLE1BQU12QixVQUFVO1lBQzVCRCxXQUFXd0IsTUFBTUUsV0FBVztRQUM5QjtJQUNGO0lBRUEsZUFBZTtJQUNmSixTQUFTQyxPQUFPLENBQUNJLENBQUFBO1FBQ2Z0QyxXQUFXb0MsSUFBSSxDQUFDO1lBQ2RQLElBQUksV0FBc0IsT0FBWFMsUUFBUVQsRUFBRTtZQUN6QjFCLE1BQU07WUFDTlMsWUFBWTBCLFFBQVExQixVQUFVO1lBQzlCRCxXQUFXMkIsUUFBUUMsV0FBVztZQUM5QjFCLFFBQVF5QixRQUFRekIsTUFBTTtZQUN0QmUsU0FBU1UsUUFBUUUsS0FBSztRQUN4QjtJQUNGO0lBRUEsd0NBQXdDO0lBQ3hDLE9BQU94QyxXQUFXeUMsSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQU1BLEVBQUVoQyxTQUFTLENBQUNRLE9BQU8sS0FBS3VCLEVBQUUvQixTQUFTLENBQUNRLE9BQU87QUFDOUUiLCJzb3VyY2VzIjpbIkQ6XFxzdHJvbmdmaXRneW0uaWRcXGFwcFxcc3JjXFxjb21wb25lbnRzXFxmZWF0dXJlc1xcZGFzaGJvYXJkXFxyZWNlbnQtYWN0aXZpdGllcy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9jYXJkJ1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbidcbmltcG9ydCB7IEJhZGdlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2JhZGdlJ1xuaW1wb3J0IHsgU21hcnRwaG9uZSwgQ3JlZGl0Q2FyZCwgVXNlclBsdXMgfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5pbXBvcnQgeyBmb3JtYXRDdXJyZW5jeSwgZm9ybWF0RGF0ZVRpbWUgfSBmcm9tICdAL2xpYi91dGlscydcbmltcG9ydCB7IFZpc2l0LCBQYXltZW50IH0gZnJvbSAnQC90eXBlcydcblxuaW50ZXJmYWNlIEFjdGl2aXR5IHtcbiAgaWQ6IHN0cmluZ1xuICB0eXBlOiAnY2hlY2tpbicgfCAncGF5bWVudCcgfCAncmVnaXN0cmF0aW9uJ1xuICBtZW1iZXJOYW1lOiBzdHJpbmdcbiAgdGltZXN0YW1wOiBEYXRlXG4gIGRldGFpbHM/OiBzdHJpbmdcbiAgYW1vdW50PzogbnVtYmVyXG59XG5cbmludGVyZmFjZSBSZWNlbnRBY3Rpdml0aWVzUHJvcHMge1xuICBhY3Rpdml0aWVzOiBBY3Rpdml0eVtdXG4gIG1heEl0ZW1zPzogbnVtYmVyXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBSZWNlbnRBY3Rpdml0aWVzKHsgYWN0aXZpdGllcywgbWF4SXRlbXMgPSA1IH06IFJlY2VudEFjdGl2aXRpZXNQcm9wcykge1xuICBjb25zdCBnZXRBY3Rpdml0eUljb24gPSAodHlwZTogQWN0aXZpdHlbJ3R5cGUnXSkgPT4ge1xuICAgIHN3aXRjaCAodHlwZSkge1xuICAgICAgY2FzZSAnY2hlY2tpbic6XG4gICAgICAgIHJldHVybiA8U21hcnRwaG9uZSBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtYmx1ZS02MDBcIiAvPlxuICAgICAgY2FzZSAncGF5bWVudCc6XG4gICAgICAgIHJldHVybiA8Q3JlZGl0Q2FyZCBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtZ3JlZW4tNjAwXCIgLz5cbiAgICAgIGNhc2UgJ3JlZ2lzdHJhdGlvbic6XG4gICAgICAgIHJldHVybiA8VXNlclBsdXMgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LXB1cnBsZS02MDBcIiAvPlxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGdldEFjdGl2aXR5QmFkZ2UgPSAodHlwZTogQWN0aXZpdHlbJ3R5cGUnXSkgPT4ge1xuICAgIHN3aXRjaCAodHlwZSkge1xuICAgICAgY2FzZSAnY2hlY2tpbic6XG4gICAgICAgIHJldHVybiA8QmFkZ2UgdmFyaWFudD1cImRlZmF1bHRcIiBjbGFzc05hbWU9XCJiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtODAwXCI+Q2hlY2staW48L0JhZGdlPlxuICAgICAgY2FzZSAncGF5bWVudCc6XG4gICAgICAgIHJldHVybiA8QmFkZ2UgdmFyaWFudD1cInN1Y2Nlc3NcIj5QZW1iYXlhcmFuPC9CYWRnZT5cbiAgICAgIGNhc2UgJ3JlZ2lzdHJhdGlvbic6XG4gICAgICAgIHJldHVybiA8QmFkZ2UgdmFyaWFudD1cInNlY29uZGFyeVwiPkFuZ2dvdGEgQmFydTwvQmFkZ2U+XG4gICAgfVxuICB9XG5cbiAgY29uc3QgZm9ybWF0QWN0aXZpdHlUZXh0ID0gKGFjdGl2aXR5OiBBY3Rpdml0eSkgPT4ge1xuICAgIGNvbnN0IHRpbWVBZ28gPSBnZXRUaW1lQWdvKGFjdGl2aXR5LnRpbWVzdGFtcClcblxuICAgIHN3aXRjaCAoYWN0aXZpdHkudHlwZSkge1xuICAgICAgY2FzZSAnY2hlY2tpbic6XG4gICAgICAgIHJldHVybiBgJHthY3Rpdml0eS5tZW1iZXJOYW1lfSBtZWxha3VrYW4gY2hlY2staW4gLSAke3RpbWVBZ299YFxuICAgICAgY2FzZSAncGF5bWVudCc6XG4gICAgICAgIHJldHVybiBgUGVtYmF5YXJhbiBkaXRlcmltYSBkYXJpICR7YWN0aXZpdHkubWVtYmVyTmFtZX0gLSAke3RpbWVBZ299JHthY3Rpdml0eS5hbW91bnQgPyBgICgke2Zvcm1hdEN1cnJlbmN5KGFjdGl2aXR5LmFtb3VudCl9KWAgOiAnJ31gXG4gICAgICBjYXNlICdyZWdpc3RyYXRpb24nOlxuICAgICAgICByZXR1cm4gYEFuZ2dvdGEgYmFydSB0ZXJkYWZ0YXI6ICR7YWN0aXZpdHkubWVtYmVyTmFtZX0gLSAke3RpbWVBZ299YFxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGdldFRpbWVBZ28gPSAodGltZXN0YW1wOiBEYXRlKSA9PiB7XG4gICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKVxuICAgIGNvbnN0IGRpZmZJbk1pbnV0ZXMgPSBNYXRoLmZsb29yKChub3cuZ2V0VGltZSgpIC0gdGltZXN0YW1wLmdldFRpbWUoKSkgLyAoMTAwMCAqIDYwKSlcblxuICAgIGlmIChkaWZmSW5NaW51dGVzIDwgMSkgcmV0dXJuICdiYXJ1IHNhamEnXG4gICAgaWYgKGRpZmZJbk1pbnV0ZXMgPCA2MCkgcmV0dXJuIGAke2RpZmZJbk1pbnV0ZXN9IG1lbml0IGxhbHVgXG5cbiAgICBjb25zdCBkaWZmSW5Ib3VycyA9IE1hdGguZmxvb3IoZGlmZkluTWludXRlcyAvIDYwKVxuICAgIGlmIChkaWZmSW5Ib3VycyA8IDI0KSByZXR1cm4gYCR7ZGlmZkluSG91cnN9IGphbSBsYWx1YFxuXG4gICAgY29uc3QgZGlmZkluRGF5cyA9IE1hdGguZmxvb3IoZGlmZkluSG91cnMgLyAyNClcbiAgICByZXR1cm4gYCR7ZGlmZkluRGF5c30gaGFyaSBsYWx1YFxuICB9XG5cbiAgY29uc3QgZGlzcGxheWVkQWN0aXZpdGllcyA9IGFjdGl2aXRpZXMuc2xpY2UoMCwgbWF4SXRlbXMpXG5cbiAgcmV0dXJuIChcbiAgICA8Q2FyZD5cbiAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZFwiPkFrdGl2aXRhcyBUZXJiYXJ1PC9DYXJkVGl0bGU+XG4gICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAge2Rpc3BsYXllZEFjdGl2aXRpZXMubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmQgdGV4dC1jZW50ZXIgcHktNFwiPlxuICAgICAgICAgICAgICBUaWRhayBhZGEgYWt0aXZpdGFzIHRlcmJhcnVcbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICApIDogKFxuICAgICAgICAgICAgZGlzcGxheWVkQWN0aXZpdGllcy5tYXAoKGFjdGl2aXR5KSA9PiAoXG4gICAgICAgICAgICAgIDxkaXYga2V5PXthY3Rpdml0eS5pZH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTAgbXQtMC41XCI+XG4gICAgICAgICAgICAgICAgICB7Z2V0QWN0aXZpdHlJY29uKGFjdGl2aXR5LnR5cGUpfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG1pbi13LTBcIj5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICAgIHtmb3JtYXRBY3Rpdml0eVRleHQoYWN0aXZpdHkpfVxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAge2FjdGl2aXR5LmRldGFpbHMgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2FjdGl2aXR5LmRldGFpbHN9XG4gICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgICAgICAgICB7Z2V0QWN0aXZpdHlCYWRnZShhY3Rpdml0eS50eXBlKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApKVxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgICBcbiAgICAgICAge2FjdGl2aXRpZXMubGVuZ3RoID4gbWF4SXRlbXMgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIHNpemU9XCJzbVwiPlxuICAgICAgICAgICAgICBWaWV3IEFsbCBBY3Rpdml0aWVzXG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgPC9DYXJkPlxuICApXG59XG5cbi8vIEhlbHBlciBmdW5jdGlvbiB0byBjb252ZXJ0IHZpc2l0cyBhbmQgcGF5bWVudHMgdG8gYWN0aXZpdGllc1xuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZUFjdGl2aXRpZXNGcm9tRGF0YShcbiAgdmlzaXRzOiBWaXNpdFtdLCBcbiAgcGF5bWVudHM6IFBheW1lbnRbXVxuKTogQWN0aXZpdHlbXSB7XG4gIGNvbnN0IGFjdGl2aXRpZXM6IEFjdGl2aXR5W10gPSBbXVxuXG4gIC8vIEFkZCBjaGVjay1pbnNcbiAgdmlzaXRzLmZvckVhY2godmlzaXQgPT4ge1xuICAgIGFjdGl2aXRpZXMucHVzaCh7XG4gICAgICBpZDogYHZpc2l0LSR7dmlzaXQuaWR9YCxcbiAgICAgIHR5cGU6ICdjaGVja2luJyxcbiAgICAgIG1lbWJlck5hbWU6IHZpc2l0Lm1lbWJlck5hbWUsXG4gICAgICB0aW1lc3RhbXA6IHZpc2l0LmNoZWNrSW5UaW1lLFxuICAgIH0pXG4gIH0pXG5cbiAgLy8gQWRkIHBheW1lbnRzXG4gIHBheW1lbnRzLmZvckVhY2gocGF5bWVudCA9PiB7XG4gICAgYWN0aXZpdGllcy5wdXNoKHtcbiAgICAgIGlkOiBgcGF5bWVudC0ke3BheW1lbnQuaWR9YCxcbiAgICAgIHR5cGU6ICdwYXltZW50JyxcbiAgICAgIG1lbWJlck5hbWU6IHBheW1lbnQubWVtYmVyTmFtZSxcbiAgICAgIHRpbWVzdGFtcDogcGF5bWVudC5wYXltZW50RGF0ZSxcbiAgICAgIGFtb3VudDogcGF5bWVudC5hbW91bnQsXG4gICAgICBkZXRhaWxzOiBwYXltZW50Lm5vdGVzLFxuICAgIH0pXG4gIH0pXG5cbiAgLy8gU29ydCBieSB0aW1lc3RhbXAgKG1vc3QgcmVjZW50IGZpcnN0KVxuICByZXR1cm4gYWN0aXZpdGllcy5zb3J0KChhLCBiKSA9PiBiLnRpbWVzdGFtcC5nZXRUaW1lKCkgLSBhLnRpbWVzdGFtcC5nZXRUaW1lKCkpXG59XG4iXSwibmFtZXMiOlsiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIkJ1dHRvbiIsIkJhZGdlIiwiU21hcnRwaG9uZSIsIkNyZWRpdENhcmQiLCJVc2VyUGx1cyIsImZvcm1hdEN1cnJlbmN5IiwiUmVjZW50QWN0aXZpdGllcyIsImFjdGl2aXRpZXMiLCJtYXhJdGVtcyIsImdldEFjdGl2aXR5SWNvbiIsInR5cGUiLCJjbGFzc05hbWUiLCJnZXRBY3Rpdml0eUJhZGdlIiwidmFyaWFudCIsImZvcm1hdEFjdGl2aXR5VGV4dCIsImFjdGl2aXR5IiwidGltZUFnbyIsImdldFRpbWVBZ28iLCJ0aW1lc3RhbXAiLCJtZW1iZXJOYW1lIiwiYW1vdW50Iiwibm93IiwiRGF0ZSIsImRpZmZJbk1pbnV0ZXMiLCJNYXRoIiwiZmxvb3IiLCJnZXRUaW1lIiwiZGlmZkluSG91cnMiLCJkaWZmSW5EYXlzIiwiZGlzcGxheWVkQWN0aXZpdGllcyIsInNsaWNlIiwiZGl2IiwibGVuZ3RoIiwicCIsIm1hcCIsImRldGFpbHMiLCJpZCIsInNpemUiLCJjcmVhdGVBY3Rpdml0aWVzRnJvbURhdGEiLCJ2aXNpdHMiLCJwYXltZW50cyIsImZvckVhY2giLCJ2aXNpdCIsInB1c2giLCJjaGVja0luVGltZSIsInBheW1lbnQiLCJwYXltZW50RGF0ZSIsIm5vdGVzIiwic29ydCIsImEiLCJiIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/features/dashboard/recent-activities.tsx\n"));

/***/ })

});