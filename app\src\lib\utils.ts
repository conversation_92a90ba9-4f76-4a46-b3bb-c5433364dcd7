import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
  }).format(amount)
}

export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(date)
}

export function formatDateTime(date: Date): string {
  return new Intl.DateTimeFormat('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date)
}

export function generateMemberId(count: number): string {
  return `GYM${String(count + 1).padStart(3, '0')}`
}

export function calculateExpiryDate(startDate: Date, packageType: string): Date {
  const expiry = new Date(startDate)
  
  switch (packageType) {
    case 'daily':
      expiry.setDate(expiry.getDate() + 1)
      break
    case 'weekly':
      expiry.setDate(expiry.getDate() + 7)
      break
    case 'monthly-basic':
    case 'monthly-unlimited':
      expiry.setMonth(expiry.getMonth() + 1)
      break
    case 'quarterly':
      expiry.setMonth(expiry.getMonth() + 3)
      break
    default:
      expiry.setMonth(expiry.getMonth() + 1)
  }
  
  return expiry
}

export function getDaysRemaining(expiryDate: Date): number {
  const today = new Date()
  const diffTime = expiryDate.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays
}

export function getMembershipStatus(expiryDate: Date): 'active' | 'expires-soon' | 'expired' {
  const daysRemaining = getDaysRemaining(expiryDate)

  if (daysRemaining < 0) return 'expired'
  if (daysRemaining <= 7) return 'expires-soon'
  return 'active'
}

export function getPackageDisplayName(packageType: string): string {
  switch (packageType) {
    case 'daily':
      return 'Paket Harian'
    case 'weekly':
      return 'Paket Mingguan'
    case 'monthly-basic':
      return 'Paket Bulanan Basic'
    case 'monthly-unlimited':
      return 'Paket Bulanan Unlimited'
    case 'quarterly':
      return 'Paket Triwulan'
    default:
      return packageType
  }
}
