'use client'

import { useState, useMemo } from 'react'
import { Search, ArrowLeft, Smartphone, CreditCard } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Member } from '@/types'
import { mockMembers } from '@/data/mockData'
import { formatDate, getMembershipStatus, getDaysRemaining } from '@/lib/utils'

interface ManualSearchProps {
  onBack: () => void
  onCheckIn: (member: Member) => void
  onRenew: (member: Member) => void
}

export function ManualSearch({ onBack, onCheckIn, onRenew }: ManualSearchProps) {
  const [searchQuery, setSearchQuery] = useState('')

  // Filter members based on search query
  const searchResults = useMemo(() => {
    if (searchQuery.trim() === '') return []
    
    const query = searchQuery.toLowerCase().trim()
    return mockMembers.filter(member => 
      member.fullName.toLowerCase().includes(query) ||
      member.phoneNumber.includes(query) ||
      member.memberId.toLowerCase().includes(query)
    ).slice(0, 10) // Limit to 10 results
  }, [searchQuery])

  const getStatusBadge = (member: Member) => {
    const status = getMembershipStatus(member.expiryDate)
    switch (status) {
      case 'active':
        return <Badge variant="success">🟢 Active</Badge>
      case 'expires-soon':
        return <Badge variant="warning">🟡 Expires Soon</Badge>
      case 'expired':
        return <Badge variant="destructive">🔴 Expired</Badge>
    }
  }

  const getStatusText = (member: Member) => {
    const status = getMembershipStatus(member.expiryDate)
    const daysRemaining = getDaysRemaining(member.expiryDate)
    
    if (status === 'expired') {
      return `Expired: ${formatDate(member.expiryDate)} (${Math.abs(daysRemaining)} days ago)`
    }
    return `Expires: ${formatDate(member.expiryDate)} (${daysRemaining} days left)`
  }

  const getLastVisitText = (member: Member) => {
    if (!member.lastVisit) return 'Never visited'
    
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - member.lastVisit.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Last visit: Just now'
    if (diffInHours < 24) return `Last visit: ${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`
    
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays === 0) return 'Last visit: Today'
    if (diffInDays === 1) return 'Last visit: Yesterday'
    if (diffInDays < 7) return `Last visit: ${diffInDays} days ago`
    
    return `Last visit: ${formatDate(member.lastVisit)}`
  }

  const getPackageDisplayName = (packageType: string) => {
    switch (packageType) {
      case 'daily':
        return 'Daily Pass'
      case 'weekly':
        return 'Weekly Pass'
      case 'monthly-basic':
        return 'Monthly Basic'
      case 'monthly-unlimited':
        return 'Monthly Unlimited'
      case 'quarterly':
        return 'Quarterly'
      default:
        return packageType
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="icon" onClick={onBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <CardTitle className="flex items-center">
            <Search className="h-5 w-5 mr-2" />
            Pencarian Anggota Manual
          </CardTitle>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search Input */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Cari berdasarkan nama, telepon, atau ID anggota..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 text-lg h-12"
            autoFocus
          />
        </div>

        {/* Search Results */}
        <div className="space-y-3">
          {searchQuery.trim() === '' ? (
            <div className="text-center py-8 text-muted-foreground">
              <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Mulai mengetik untuk mencari anggota</p>
              <p className="text-sm">Cari berdasarkan nama, nomor telepon, atau ID anggota</p>
            </div>
          ) : searchResults.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <p>Tidak ada anggota yang ditemukan untuk "{searchQuery}"</p>
              <p className="text-sm">Coba kata kunci yang berbeda</p>
            </div>
          ) : (
            <>
              <div className="text-sm text-muted-foreground">
                Hasil Pencarian ({searchResults.length} ditemukan):
              </div>
              
              {searchResults.map((member) => {
                const status = getMembershipStatus(member.expiryDate)
                const isExpired = status === 'expired'
                
                return (
                  <Card key={member.id} className="border-l-4 border-l-primary">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        {/* Member Info */}
                        <div className="flex items-start space-x-3 flex-1">
                          {/* Avatar */}
                          <div className="w-12 h-12 bg-muted rounded-full flex items-center justify-center flex-shrink-0">
                            {member.photoURL ? (
                              <img 
                                src={member.photoURL} 
                                alt={member.fullName}
                                className="w-12 h-12 rounded-full object-cover"
                              />
                            ) : (
                              <span className="text-lg font-semibold text-muted-foreground">
                                📷
                              </span>
                            )}
                          </div>

                          {/* Details */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2 mb-2">
                              <span className="font-medium text-sm text-muted-foreground">
                                {member.memberId}
                              </span>
                              <span className="font-semibold text-lg">{member.fullName}</span>
                              <span className="text-sm text-muted-foreground">
                                📱 {member.phoneNumber}
                              </span>
                              {getStatusBadge(member)}
                            </div>
                            
                            <div className="text-sm text-muted-foreground space-y-1">
                              <div>
                                {getPackageDisplayName(member.packageType)} • {getStatusText(member)}
                              </div>
                              <div>
                                {getLastVisitText(member)}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center space-x-2 mt-4">
                        {isExpired ? (
                          <>
                            <div className="flex-1 p-3 bg-destructive/10 rounded-md text-center">
                              <div className="text-sm font-medium text-destructive">
                                ❌ KEANGGOTAAN KEDALUWARSA
                              </div>
                            </div>
                            <Button
                              onClick={() => onRenew(member)}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              <CreditCard className="h-4 w-4 mr-2" />
                              Perpanjang Keanggotaan
                            </Button>
                          </>
                        ) : (
                          <Button
                            onClick={() => onCheckIn(member)}
                            className="w-full h-12 text-lg"
                            size="lg"
                          >
                            <Smartphone className="h-5 w-5 mr-2" />
                            CHECK-IN SEKARANG
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </>
          )}
        </div>

        {/* Instructions */}
        <div className="text-sm text-muted-foreground text-center p-4 bg-muted/50 rounded-md">
          <p><strong>Tips:</strong> Anda dapat mencari berdasarkan:</p>
          <p>• Nama anggota (contoh: "John Doe")</p>
          <p>• Nomor telepon (contoh: "812-3456")</p>
          <p>• ID anggota (contoh: "GYM001")</p>
        </div>
      </CardContent>
    </Card>
  )
}
