"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/checkin/page",{

/***/ "(app-pages-browser)/./src/components/features/checkin/qr-scanner.tsx":
/*!********************************************************!*\
  !*** ./src/components/features/checkin/qr-scanner.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QRScanner: () => (/* binding */ QRScanner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Camera_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ QRScanner auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction QRScanner(param) {\n    let { onScanSuccess, onScanError, onManualSearch } = param;\n    _s();\n    const [isScanning, setIsScanning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasPermission, setHasPermission] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QRScanner.useEffect\": ()=>{\n            return ({\n                \"QRScanner.useEffect\": ()=>{\n                    // Cleanup on unmount\n                    stopScanning();\n                }\n            })[\"QRScanner.useEffect\"];\n        }\n    }[\"QRScanner.useEffect\"], []);\n    const startScanning = async ()=>{\n        try {\n            const stream = await navigator.mediaDevices.getUserMedia({\n                video: {\n                    facingMode: 'environment',\n                    width: {\n                        ideal: 640\n                    },\n                    height: {\n                        ideal: 480\n                    }\n                }\n            });\n            if (videoRef.current) {\n                videoRef.current.srcObject = stream;\n                streamRef.current = stream;\n                setIsScanning(true);\n                setHasPermission(true);\n                // Start QR code detection simulation\n                simulateQRDetection();\n            }\n        } catch (error) {\n            console.error('Error accessing camera:', error);\n            setHasPermission(false);\n            onScanError('Camera access denied or not available');\n        }\n    };\n    const stopScanning = ()=>{\n        if (streamRef.current) {\n            streamRef.current.getTracks().forEach((track)=>track.stop());\n            streamRef.current = null;\n        }\n        setIsScanning(false);\n    };\n    // Simulate QR code detection for demo purposes\n    const simulateQRDetection = ()=>{\n        // In a real implementation, you would use a QR code detection library\n        // For demo, we'll simulate finding a QR code after a few seconds\n        setTimeout(()=>{\n            if (isScanning) {\n                // Simulate scanning a member's QR code\n                const mockMemberIds = [\n                    'GYM001',\n                    'GYM045',\n                    'GYM089',\n                    'GYM123',\n                    'GYM156'\n                ];\n                const randomMemberId = mockMemberIds[Math.floor(Math.random() * mockMemberIds.length)];\n                onScanSuccess(randomMemberId);\n                stopScanning();\n            }\n        }, 3000);\n    };\n    if (hasPermission === false) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-5 w-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this),\n                            \"QR Code Scanner\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"text-center space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-muted-foreground\",\n                            children: \"Akses kamera diperlukan untuk memindai kode QR\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: startScanning,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                \"Aktifkan Kamera\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: \"Atau gunakan pencarian manual\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outline\",\n                            onClick: onManualSearch,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this),\n                                \"Pencarian Manual\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-5 w-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this),\n                                \"QR Code Scanner\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this),\n                        isScanning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: stopScanning,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this),\n                                \"Stop\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative aspect-video bg-black rounded-lg overflow-hidden\",\n                        children: isScanning ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                    ref: videoRef,\n                                    autoPlay: true,\n                                    playsInline: true,\n                                    muted: true,\n                                    className: \"w-full h-full object-cover\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-48 h-48 border-2 border-white border-dashed rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl mb-2\",\n                                                    children: \"\\uD83D\\uDCF1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: \"Posisikan kode QR di sini\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-4 left-4 right-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-black/50 text-white px-3 py-2 rounded-md text-sm text-center\",\n                                        children: \"\\uD83D\\uDD0D Scanning for QR Code...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full flex items-center justify-center text-white\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg mb-2\",\n                                        children: \"Camera Ready\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm opacity-75\",\n                                        children: \"Click start to begin scanning\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            !isScanning ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: startScanning,\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Start Scanning\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: stopScanning,\n                                variant: \"outline\",\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Stop Scanning\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                onClick: onManualSearch,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Manual Search\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-muted-foreground text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Hold the member's QR code in front of the camera\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"The system will automatically detect and process the check-in\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\features\\\\checkin\\\\qr-scanner.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\n_s(QRScanner, \"6PPiyJLM/A+uW3DTlAQRYftQfD8=\");\n_c = QRScanner;\nvar _c;\n$RefreshReg$(_c, \"QRScanner\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2ZlYXR1cmVzL2NoZWNraW4vcXItc2Nhbm5lci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFFbUQ7QUFDSDtBQUNEO0FBQ2dDO0FBUXhFLFNBQVNXLFVBQVUsS0FBOEQ7UUFBOUQsRUFBRUMsYUFBYSxFQUFFQyxXQUFXLEVBQUVDLGNBQWMsRUFBa0IsR0FBOUQ7O0lBQ3hCLE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHaEIsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDaUIsZUFBZUMsaUJBQWlCLEdBQUdsQiwrQ0FBUUEsQ0FBaUI7SUFDbkUsTUFBTW1CLFdBQVdsQiw2Q0FBTUEsQ0FBbUI7SUFDMUMsTUFBTW1CLFlBQVluQiw2Q0FBTUEsQ0FBcUI7SUFFN0NDLGdEQUFTQTsrQkFBQztZQUNSO3VDQUFPO29CQUNMLHFCQUFxQjtvQkFDckJtQjtnQkFDRjs7UUFDRjs4QkFBRyxFQUFFO0lBRUwsTUFBTUMsZ0JBQWdCO1FBQ3BCLElBQUk7WUFDRixNQUFNQyxTQUFTLE1BQU1DLFVBQVVDLFlBQVksQ0FBQ0MsWUFBWSxDQUFDO2dCQUN2REMsT0FBTztvQkFDTEMsWUFBWTtvQkFDWkMsT0FBTzt3QkFBRUMsT0FBTztvQkFBSTtvQkFDcEJDLFFBQVE7d0JBQUVELE9BQU87b0JBQUk7Z0JBQ3ZCO1lBQ0Y7WUFFQSxJQUFJWCxTQUFTYSxPQUFPLEVBQUU7Z0JBQ3BCYixTQUFTYSxPQUFPLENBQUNDLFNBQVMsR0FBR1Y7Z0JBQzdCSCxVQUFVWSxPQUFPLEdBQUdUO2dCQUNwQlAsY0FBYztnQkFDZEUsaUJBQWlCO2dCQUVqQixxQ0FBcUM7Z0JBQ3JDZ0I7WUFDRjtRQUNGLEVBQUUsT0FBT0MsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsMkJBQTJCQTtZQUN6Q2pCLGlCQUFpQjtZQUNqQkwsWUFBWTtRQUNkO0lBQ0Y7SUFFQSxNQUFNUSxlQUFlO1FBQ25CLElBQUlELFVBQVVZLE9BQU8sRUFBRTtZQUNyQlosVUFBVVksT0FBTyxDQUFDSyxTQUFTLEdBQUdDLE9BQU8sQ0FBQ0MsQ0FBQUEsUUFBU0EsTUFBTUMsSUFBSTtZQUN6RHBCLFVBQVVZLE9BQU8sR0FBRztRQUN0QjtRQUNBaEIsY0FBYztJQUNoQjtJQUVBLCtDQUErQztJQUMvQyxNQUFNa0Isc0JBQXNCO1FBQzFCLHNFQUFzRTtRQUN0RSxpRUFBaUU7UUFDakVPLFdBQVc7WUFDVCxJQUFJMUIsWUFBWTtnQkFDZCx1Q0FBdUM7Z0JBQ3ZDLE1BQU0yQixnQkFBZ0I7b0JBQUM7b0JBQVU7b0JBQVU7b0JBQVU7b0JBQVU7aUJBQVM7Z0JBQ3hFLE1BQU1DLGlCQUFpQkQsYUFBYSxDQUFDRSxLQUFLQyxLQUFLLENBQUNELEtBQUtFLE1BQU0sS0FBS0osY0FBY0ssTUFBTSxFQUFFO2dCQUN0Rm5DLGNBQWMrQjtnQkFDZHRCO1lBQ0Y7UUFDRixHQUFHO0lBQ0w7SUFFQSxJQUFJSixrQkFBa0IsT0FBTztRQUMzQixxQkFDRSw4REFBQ1YscURBQUlBOzs4QkFDSCw4REFBQ0UsMkRBQVVBOzhCQUNULDRFQUFDQywwREFBU0E7d0JBQUNzQyxXQUFVOzswQ0FDbkIsOERBQUM3QywyRkFBTUE7Z0NBQUM2QyxXQUFVOzs7Ozs7NEJBQWlCOzs7Ozs7Ozs7Ozs7OEJBSXZDLDhEQUFDeEMsNERBQVdBO29CQUFDd0MsV0FBVTs7c0NBQ3JCLDhEQUFDQzs0QkFBSUQsV0FBVTtzQ0FBd0I7Ozs7OztzQ0FHdkMsOERBQUMxQyx5REFBTUE7NEJBQUM0QyxTQUFTNUI7OzhDQUNmLDhEQUFDbkIsMkZBQU1BO29DQUFDNkMsV0FBVTs7Ozs7O2dDQUFpQjs7Ozs7OztzQ0FHckMsOERBQUNDOzRCQUFJRCxXQUFVO3NDQUFnQzs7Ozs7O3NDQUcvQyw4REFBQzFDLHlEQUFNQTs0QkFBQzZDLFNBQVE7NEJBQVVELFNBQVNwQzs7OENBQ2pDLDhEQUFDViwyRkFBTUE7b0NBQUM0QyxXQUFVOzs7Ozs7Z0NBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBTTdDO0lBRUEscUJBQ0UsOERBQUN6QyxxREFBSUE7OzBCQUNILDhEQUFDRSwyREFBVUE7MEJBQ1QsNEVBQUNDLDBEQUFTQTtvQkFBQ3NDLFdBQVU7O3NDQUNuQiw4REFBQ0M7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDN0MsMkZBQU1BO29DQUFDNkMsV0FBVTs7Ozs7O2dDQUFpQjs7Ozs7Ozt3QkFHcENqQyw0QkFDQyw4REFBQ1QseURBQU1BOzRCQUFDNkMsU0FBUTs0QkFBVUMsTUFBSzs0QkFBS0YsU0FBUzdCOzs4Q0FDM0MsOERBQUNoQiwyRkFBQ0E7b0NBQUMyQyxXQUFVOzs7Ozs7Z0NBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTXRDLDhEQUFDeEMsNERBQVdBO2dCQUFDd0MsV0FBVTs7a0NBRXJCLDhEQUFDQzt3QkFBSUQsV0FBVTtrQ0FDWmpDLDJCQUNDOzs4Q0FDRSw4REFBQ1k7b0NBQ0MwQixLQUFLbEM7b0NBQ0xtQyxRQUFRO29DQUNSQyxXQUFXO29DQUNYQyxLQUFLO29DQUNMUixXQUFVOzs7Ozs7OENBSVosOERBQUNDO29DQUFJRCxXQUFVOzhDQUNiLDRFQUFDQzt3Q0FBSUQsV0FBVTtrREFDYiw0RUFBQ0M7NENBQUlELFdBQVU7OzhEQUNiLDhEQUFDQztvREFBSUQsV0FBVTs4REFBZ0I7Ozs7Ozs4REFDL0IsOERBQUNDO29EQUFJRCxXQUFVOzhEQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQU0vQiw4REFBQ0M7b0NBQUlELFdBQVU7OENBQ2IsNEVBQUNDO3dDQUFJRCxXQUFVO2tEQUFrRTs7Ozs7Ozs7Ozs7O3lEQU1yRiw4REFBQ0M7NEJBQUlELFdBQVU7c0NBQ2IsNEVBQUNDO2dDQUFJRCxXQUFVOztrREFDYiw4REFBQzdDLDJGQUFNQTt3Q0FBQzZDLFdBQVU7Ozs7OztrREFDbEIsOERBQUNDO3dDQUFJRCxXQUFVO2tEQUFlOzs7Ozs7a0RBQzlCLDhEQUFDQzt3Q0FBSUQsV0FBVTtrREFBcUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBTzVDLDhEQUFDQzt3QkFBSUQsV0FBVTs7NEJBQ1osQ0FBQ2pDLDJCQUNBLDhEQUFDVCx5REFBTUE7Z0NBQUM0QyxTQUFTNUI7Z0NBQWUwQixXQUFVOztrREFDeEMsOERBQUM3QywyRkFBTUE7d0NBQUM2QyxXQUFVOzs7Ozs7b0NBQWlCOzs7Ozs7cURBSXJDLDhEQUFDMUMseURBQU1BO2dDQUFDNEMsU0FBUzdCO2dDQUFjOEIsU0FBUTtnQ0FBVUgsV0FBVTs7a0RBQ3pELDhEQUFDM0MsMkZBQUNBO3dDQUFDMkMsV0FBVTs7Ozs7O29DQUFpQjs7Ozs7OzswQ0FLbEMsOERBQUMxQyx5REFBTUE7Z0NBQUM2QyxTQUFRO2dDQUFVRCxTQUFTcEM7O2tEQUNqQyw4REFBQ1YsMkZBQU1BO3dDQUFDNEMsV0FBVTs7Ozs7O29DQUFpQjs7Ozs7Ozs7Ozs7OztrQ0FNdkMsOERBQUNDO3dCQUFJRCxXQUFVOzswQ0FDYiw4REFBQ1M7MENBQUU7Ozs7OzswQ0FDSCw4REFBQ0E7MENBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUtiO0dBaExnQjlDO0tBQUFBIiwic291cmNlcyI6WyJEOlxcc3Ryb25nZml0Z3ltLmlkXFxhcHBcXHNyY1xcY29tcG9uZW50c1xcZmVhdHVyZXNcXGNoZWNraW5cXHFyLXNjYW5uZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlUmVmLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IENhbWVyYSwgU2VhcmNoLCBYIH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbidcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvY2FyZCdcblxuaW50ZXJmYWNlIFFSU2Nhbm5lclByb3BzIHtcbiAgb25TY2FuU3VjY2VzczogKG1lbWJlcklkOiBzdHJpbmcpID0+IHZvaWRcbiAgb25TY2FuRXJyb3I6IChlcnJvcjogc3RyaW5nKSA9PiB2b2lkXG4gIG9uTWFudWFsU2VhcmNoOiAoKSA9PiB2b2lkXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBRUlNjYW5uZXIoeyBvblNjYW5TdWNjZXNzLCBvblNjYW5FcnJvciwgb25NYW51YWxTZWFyY2ggfTogUVJTY2FubmVyUHJvcHMpIHtcbiAgY29uc3QgW2lzU2Nhbm5pbmcsIHNldElzU2Nhbm5pbmddID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtoYXNQZXJtaXNzaW9uLCBzZXRIYXNQZXJtaXNzaW9uXSA9IHVzZVN0YXRlPGJvb2xlYW4gfCBudWxsPihudWxsKVxuICBjb25zdCB2aWRlb1JlZiA9IHVzZVJlZjxIVE1MVmlkZW9FbGVtZW50PihudWxsKVxuICBjb25zdCBzdHJlYW1SZWYgPSB1c2VSZWY8TWVkaWFTdHJlYW0gfCBudWxsPihudWxsKVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIC8vIENsZWFudXAgb24gdW5tb3VudFxuICAgICAgc3RvcFNjYW5uaW5nKClcbiAgICB9XG4gIH0sIFtdKVxuXG4gIGNvbnN0IHN0YXJ0U2Nhbm5pbmcgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHN0cmVhbSA9IGF3YWl0IG5hdmlnYXRvci5tZWRpYURldmljZXMuZ2V0VXNlck1lZGlhKHsgXG4gICAgICAgIHZpZGVvOiB7IFxuICAgICAgICAgIGZhY2luZ01vZGU6ICdlbnZpcm9ubWVudCcsIC8vIFVzZSBiYWNrIGNhbWVyYSBpZiBhdmFpbGFibGVcbiAgICAgICAgICB3aWR0aDogeyBpZGVhbDogNjQwIH0sXG4gICAgICAgICAgaGVpZ2h0OiB7IGlkZWFsOiA0ODAgfVxuICAgICAgICB9IFxuICAgICAgfSlcbiAgICAgIFxuICAgICAgaWYgKHZpZGVvUmVmLmN1cnJlbnQpIHtcbiAgICAgICAgdmlkZW9SZWYuY3VycmVudC5zcmNPYmplY3QgPSBzdHJlYW1cbiAgICAgICAgc3RyZWFtUmVmLmN1cnJlbnQgPSBzdHJlYW1cbiAgICAgICAgc2V0SXNTY2FubmluZyh0cnVlKVxuICAgICAgICBzZXRIYXNQZXJtaXNzaW9uKHRydWUpXG4gICAgICAgIFxuICAgICAgICAvLyBTdGFydCBRUiBjb2RlIGRldGVjdGlvbiBzaW11bGF0aW9uXG4gICAgICAgIHNpbXVsYXRlUVJEZXRlY3Rpb24oKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBhY2Nlc3NpbmcgY2FtZXJhOicsIGVycm9yKVxuICAgICAgc2V0SGFzUGVybWlzc2lvbihmYWxzZSlcbiAgICAgIG9uU2NhbkVycm9yKCdDYW1lcmEgYWNjZXNzIGRlbmllZCBvciBub3QgYXZhaWxhYmxlJylcbiAgICB9XG4gIH1cblxuICBjb25zdCBzdG9wU2Nhbm5pbmcgPSAoKSA9PiB7XG4gICAgaWYgKHN0cmVhbVJlZi5jdXJyZW50KSB7XG4gICAgICBzdHJlYW1SZWYuY3VycmVudC5nZXRUcmFja3MoKS5mb3JFYWNoKHRyYWNrID0+IHRyYWNrLnN0b3AoKSlcbiAgICAgIHN0cmVhbVJlZi5jdXJyZW50ID0gbnVsbFxuICAgIH1cbiAgICBzZXRJc1NjYW5uaW5nKGZhbHNlKVxuICB9XG5cbiAgLy8gU2ltdWxhdGUgUVIgY29kZSBkZXRlY3Rpb24gZm9yIGRlbW8gcHVycG9zZXNcbiAgY29uc3Qgc2ltdWxhdGVRUkRldGVjdGlvbiA9ICgpID0+IHtcbiAgICAvLyBJbiBhIHJlYWwgaW1wbGVtZW50YXRpb24sIHlvdSB3b3VsZCB1c2UgYSBRUiBjb2RlIGRldGVjdGlvbiBsaWJyYXJ5XG4gICAgLy8gRm9yIGRlbW8sIHdlJ2xsIHNpbXVsYXRlIGZpbmRpbmcgYSBRUiBjb2RlIGFmdGVyIGEgZmV3IHNlY29uZHNcbiAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIGlmIChpc1NjYW5uaW5nKSB7XG4gICAgICAgIC8vIFNpbXVsYXRlIHNjYW5uaW5nIGEgbWVtYmVyJ3MgUVIgY29kZVxuICAgICAgICBjb25zdCBtb2NrTWVtYmVySWRzID0gWydHWU0wMDEnLCAnR1lNMDQ1JywgJ0dZTTA4OScsICdHWU0xMjMnLCAnR1lNMTU2J11cbiAgICAgICAgY29uc3QgcmFuZG9tTWVtYmVySWQgPSBtb2NrTWVtYmVySWRzW01hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIG1vY2tNZW1iZXJJZHMubGVuZ3RoKV1cbiAgICAgICAgb25TY2FuU3VjY2VzcyhyYW5kb21NZW1iZXJJZClcbiAgICAgICAgc3RvcFNjYW5uaW5nKClcbiAgICAgIH1cbiAgICB9LCAzMDAwKVxuICB9XG5cbiAgaWYgKGhhc1Blcm1pc3Npb24gPT09IGZhbHNlKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxDYXJkPlxuICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICA8Q2FtZXJhIGNsYXNzTmFtZT1cImgtNSB3LTUgbXItMlwiIC8+XG4gICAgICAgICAgICBRUiBDb2RlIFNjYW5uZXJcbiAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgc3BhY2UteS00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgIEFrc2VzIGthbWVyYSBkaXBlcmx1a2FuIHVudHVrIG1lbWluZGFpIGtvZGUgUVJcbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9e3N0YXJ0U2Nhbm5pbmd9PlxuICAgICAgICAgICAgPENhbWVyYSBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgQWt0aWZrYW4gS2FtZXJhXG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgQXRhdSBndW5ha2FuIHBlbmNhcmlhbiBtYW51YWxcbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgb25DbGljaz17b25NYW51YWxTZWFyY2h9PlxuICAgICAgICAgICAgPFNlYXJjaCBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgUGVuY2FyaWFuIE1hbnVhbFxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgPC9DYXJkPlxuICAgIClcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPENhcmQ+XG4gICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICA8Q2FtZXJhIGNsYXNzTmFtZT1cImgtNSB3LTUgbXItMlwiIC8+XG4gICAgICAgICAgICBRUiBDb2RlIFNjYW5uZXJcbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICB7aXNTY2FubmluZyAmJiAoXG4gICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgc2l6ZT1cInNtXCIgb25DbGljaz17c3RvcFNjYW5uaW5nfT5cbiAgICAgICAgICAgICAgPFggY2xhc3NOYW1lPVwiaC00IHctNCBtci0xXCIgLz5cbiAgICAgICAgICAgICAgU3RvcFxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgIHsvKiBDYW1lcmEgVmlldyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBhc3BlY3QtdmlkZW8gYmctYmxhY2sgcm91bmRlZC1sZyBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgICB7aXNTY2FubmluZyA/IChcbiAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgIDx2aWRlb1xuICAgICAgICAgICAgICAgIHJlZj17dmlkZW9SZWZ9XG4gICAgICAgICAgICAgICAgYXV0b1BsYXlcbiAgICAgICAgICAgICAgICBwbGF5c0lubGluZVxuICAgICAgICAgICAgICAgIG11dGVkXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXJcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgey8qIFNjYW5uaW5nIE92ZXJsYXkgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy00OCBoLTQ4IGJvcmRlci0yIGJvcmRlci13aGl0ZSBib3JkZXItZGFzaGVkIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIG1iLTJcIj7wn5OxPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbVwiPlBvc2lzaWthbiBrb2RlIFFSIGRpIHNpbmk8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogU2Nhbm5pbmcgQW5pbWF0aW9uICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC00IGxlZnQtNCByaWdodC00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ibGFjay81MCB0ZXh0LXdoaXRlIHB4LTMgcHktMiByb3VuZGVkLW1kIHRleHQtc20gdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIPCflI0gU2Nhbm5pbmcgZm9yIFFSIENvZGUuLi5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8Lz5cbiAgICAgICAgICApIDogKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxDYW1lcmEgY2xhc3NOYW1lPVwiaC0xMiB3LTEyIG14LWF1dG8gbWItNCBvcGFjaXR5LTUwXCIgLz5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtbGcgbWItMlwiPkNhbWVyYSBSZWFkeTwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBvcGFjaXR5LTc1XCI+Q2xpY2sgc3RhcnQgdG8gYmVnaW4gc2Nhbm5pbmc8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogQ29udHJvbHMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTJcIj5cbiAgICAgICAgICB7IWlzU2Nhbm5pbmcgPyAoXG4gICAgICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9e3N0YXJ0U2Nhbm5pbmd9IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICA8Q2FtZXJhIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgIFN0YXJ0IFNjYW5uaW5nXG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICApIDogKFxuICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXtzdG9wU2Nhbm5pbmd9IHZhcmlhbnQ9XCJvdXRsaW5lXCIgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICAgIDxYIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgIFN0b3AgU2Nhbm5pbmdcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICl9XG4gICAgICAgICAgXG4gICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIG9uQ2xpY2s9e29uTWFudWFsU2VhcmNofT5cbiAgICAgICAgICAgIDxTZWFyY2ggY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgIE1hbnVhbCBTZWFyY2hcbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEluc3RydWN0aW9ucyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxwPkhvbGQgdGhlIG1lbWJlcidzIFFSIGNvZGUgaW4gZnJvbnQgb2YgdGhlIGNhbWVyYTwvcD5cbiAgICAgICAgICA8cD5UaGUgc3lzdGVtIHdpbGwgYXV0b21hdGljYWxseSBkZXRlY3QgYW5kIHByb2Nlc3MgdGhlIGNoZWNrLWluPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgPC9DYXJkPlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VSZWYiLCJ1c2VFZmZlY3QiLCJDYW1lcmEiLCJTZWFyY2giLCJYIiwiQnV0dG9uIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIlFSU2Nhbm5lciIsIm9uU2NhblN1Y2Nlc3MiLCJvblNjYW5FcnJvciIsIm9uTWFudWFsU2VhcmNoIiwiaXNTY2FubmluZyIsInNldElzU2Nhbm5pbmciLCJoYXNQZXJtaXNzaW9uIiwic2V0SGFzUGVybWlzc2lvbiIsInZpZGVvUmVmIiwic3RyZWFtUmVmIiwic3RvcFNjYW5uaW5nIiwic3RhcnRTY2FubmluZyIsInN0cmVhbSIsIm5hdmlnYXRvciIsIm1lZGlhRGV2aWNlcyIsImdldFVzZXJNZWRpYSIsInZpZGVvIiwiZmFjaW5nTW9kZSIsIndpZHRoIiwiaWRlYWwiLCJoZWlnaHQiLCJjdXJyZW50Iiwic3JjT2JqZWN0Iiwic2ltdWxhdGVRUkRldGVjdGlvbiIsImVycm9yIiwiY29uc29sZSIsImdldFRyYWNrcyIsImZvckVhY2giLCJ0cmFjayIsInN0b3AiLCJzZXRUaW1lb3V0IiwibW9ja01lbWJlcklkcyIsInJhbmRvbU1lbWJlcklkIiwiTWF0aCIsImZsb29yIiwicmFuZG9tIiwibGVuZ3RoIiwiY2xhc3NOYW1lIiwiZGl2Iiwib25DbGljayIsInZhcmlhbnQiLCJzaXplIiwicmVmIiwiYXV0b1BsYXkiLCJwbGF5c0lubGluZSIsIm11dGVkIiwicCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/features/checkin/qr-scanner.tsx\n"));

/***/ })

});