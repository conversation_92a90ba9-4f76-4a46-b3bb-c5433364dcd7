import { Member, Payment, Visit, User, StudioSettings, Package, PackageType } from '@/types'

// Mock Users
export const mockUsers: User[] = [
  {
    id: 'user-1',
    email: '<EMAIL>',
    role: 'owner',
    displayName: 'John Owner',
    photoURL: '/avatars/owner.jpg',
    createdAt: new Date('2023-01-01'),
    lastLogin: new Date(),
  },
  {
    id: 'user-2',
    email: '<EMAIL>',
    role: 'staff',
    displayName: 'Sarah Staff',
    photoURL: '/avatars/staff.jpg',
    createdAt: new Date('2023-01-15'),
    lastLogin: new Date(),
  },
]

// Mock Packages
export const mockPackages: Record<PackageType, Package> = {
  daily: {
    id: 'pkg-daily',
    name: '<PERSON><PERSON>',
    price: 50000,
    duration: 1,
    description: 'Akses satu hari',
    features: ['Akses peralatan gym', '<PERSON>ks<PERSON> loker'],
  },
  weekly: {
    id: 'pkg-weekly',
    name: 'Pak<PERSON>guan',
    price: 200000,
    duration: 7,
    description: 'Akses unlimited 7 hari',
    features: ['Akses peralatan gym', 'Akses loker', 'Handuk gratis'],
  },
  'monthly-basic': {
    id: 'pkg-monthly-basic',
    name: 'Paket Bulanan Basic',
    price: 400000,
    duration: 30,
    description: 'Akses gym 30 hari',
    features: ['Akses peralatan gym', 'Akses loker', 'Handuk gratis'],
  },
  'monthly-unlimited': {
    id: 'pkg-monthly-unlimited',
    name: 'Paket Bulanan Unlimited',
    price: 600000,
    duration: 30,
    description: 'Akses unlimited 30 hari',
    features: ['Akses peralatan gym', 'Kelas grup', 'Akses loker', 'Handuk gratis', 'Konsultasi nutrisi'],
  },
  quarterly: {
    id: 'pkg-quarterly',
    name: 'Paket Triwulan',
    price: 1500000,
    duration: 90,
    description: 'Akses premium 90 hari',
    features: ['Akses peralatan gym', 'Kelas grup', 'Sesi personal trainer', 'Akses loker', 'Handuk gratis', 'Konsultasi nutrisi', 'Pelacakan progress'],
  },
}

// Mock Studio Settings
export const mockStudioSettings: StudioSettings = {
  name: 'StrongFit Gym',
  address: 'Jl. Sudirman No. 123, Jakarta Selatan',
  phone: '+62 21 1234 5678',
  email: '<EMAIL>',
  maxCapacity: 30,
  operatingHours: {
    open: '06:00',
    close: '22:00',
  },
  packages: mockPackages,
}

// Mock Members
export const mockMembers: Member[] = [
  {
    id: 'member-1',
    memberId: 'GYM001',
    fullName: 'John Doe',
    phoneNumber: '+62 812-3456-7890',
    email: '<EMAIL>',
    address: 'Jl. Sudirman No. 123, Jakarta',
    emergencyContact: 'Jane Doe - +62 812-9876-5432',
    photoURL: '/avatars/member1.jpg',
    packageType: 'monthly-basic',
    startDate: new Date('2023-12-15'),
    expiryDate: new Date('2024-02-14'),
    status: 'active',
    totalVisits: 47,
    lastVisit: new Date(),
    createdAt: new Date('2023-12-15'),
    createdBy: 'user-2',
  },
  {
    id: 'member-2',
    memberId: 'GYM045',
    fullName: 'Sarah Wilson',
    phoneNumber: '+62 821-9876-5432',
    email: '<EMAIL>',
    packageType: 'quarterly',
    startDate: new Date('2023-10-20'),
    expiryDate: new Date('2024-01-20'),
    status: 'expires-soon',
    totalVisits: 32,
    lastVisit: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
    createdAt: new Date('2023-10-20'),
    createdBy: 'user-2',
  },
  {
    id: 'member-3',
    memberId: 'GYM089',
    fullName: 'Mike Johnson',
    phoneNumber: '+62 856-1234-5678',
    packageType: 'monthly-unlimited',
    startDate: new Date('2023-12-10'),
    expiryDate: new Date('2024-01-10'),
    status: 'expired',
    totalVisits: 28,
    lastVisit: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 1 week ago
    createdAt: new Date('2023-12-10'),
    createdBy: 'user-2',
  },
  {
    id: 'member-4',
    memberId: 'GYM123',
    fullName: 'Lisa Wong',
    phoneNumber: '+62 877-5555-1234',
    packageType: 'weekly',
    startDate: new Date('2024-01-15'),
    expiryDate: new Date('2024-01-22'),
    status: 'active',
    totalVisits: 5,
    lastVisit: new Date(),
    createdAt: new Date('2024-01-15'),
    createdBy: 'user-2',
  },
  {
    id: 'member-5',
    memberId: 'GYM156',
    fullName: 'David Chen',
    phoneNumber: '+62 813-7777-8888',
    packageType: 'monthly-basic',
    startDate: new Date('2024-01-01'),
    expiryDate: new Date('2024-03-01'),
    status: 'active',
    totalVisits: 15,
    lastVisit: new Date(Date.now() - 24 * 60 * 60 * 1000), // Yesterday
    createdAt: new Date('2024-01-01'),
    createdBy: 'user-2',
  },
]

// Mock Payments
export const mockPayments: Payment[] = [
  {
    id: 'payment-1',
    memberId: 'member-1',
    memberName: 'John Doe',
    amount: 400000,
    method: 'transfer',
    packageType: 'monthly-basic',
    duration: 30,
    processedBy: 'user-2',
    paymentDate: new Date('2024-01-15'),
    receiptNumber: 'RCP001234',
    notes: 'Monthly renewal',
  },
  {
    id: 'payment-2',
    memberId: 'member-1',
    memberName: 'John Doe',
    amount: 400000,
    method: 'cash',
    packageType: 'monthly-basic',
    duration: 30,
    processedBy: 'user-2',
    paymentDate: new Date('2023-12-15'),
    receiptNumber: 'RCP001201',
    notes: 'Initial registration',
  },
  {
    id: 'payment-3',
    memberId: 'member-2',
    memberName: 'Sarah Wilson',
    amount: 1500000,
    method: 'transfer',
    packageType: 'quarterly',
    duration: 90,
    processedBy: 'user-2',
    paymentDate: new Date('2023-10-20'),
    receiptNumber: 'RCP001156',
  },
  {
    id: 'payment-4',
    memberId: 'member-5',
    memberName: 'David Chen',
    amount: 600000,
    method: 'qris',
    packageType: 'monthly-unlimited',
    duration: 30,
    processedBy: 'user-2',
    paymentDate: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
    receiptNumber: 'RCP001567',
  },
]

// Mock Visits
export const mockVisits: Visit[] = [
  {
    id: 'visit-1',
    memberId: 'member-1',
    memberName: 'John Doe',
    checkInTime: new Date(Date.now() - 2 * 60 * 1000), // 2 minutes ago
    processedBy: 'user-2',
    visitType: 'gym',
  },
  {
    id: 'visit-2',
    memberId: 'member-2',
    memberName: 'Sarah Wilson',
    checkInTime: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
    processedBy: 'user-2',
    visitType: 'gym',
  },
  {
    id: 'visit-3',
    memberId: 'member-4',
    memberName: 'Lisa Wong',
    checkInTime: new Date(Date.now() - 23 * 60 * 1000), // 23 minutes ago
    processedBy: 'user-2',
    visitType: 'gym',
  },
  {
    id: 'visit-4',
    memberId: 'member-5',
    memberName: 'David Chen',
    checkInTime: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
    processedBy: 'user-2',
    visitType: 'gym',
  },
  {
    id: 'visit-5',
    memberId: 'member-3',
    memberName: 'Mike Johnson',
    checkInTime: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
    processedBy: 'user-2',
    visitType: 'gym',
  },
]
