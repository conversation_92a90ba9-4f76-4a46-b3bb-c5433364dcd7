'use client'

import { useState, useRef, useEffect } from 'react'
import { Camera, Search, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

interface QRScannerProps {
  onScanSuccess: (memberId: string) => void
  onScanError: (error: string) => void
  onManualSearch: () => void
}

export function QRScanner({ onScanSuccess, onScanError, onManualSearch }: QRScannerProps) {
  const [isScanning, setIsScanning] = useState(false)
  const [hasPermission, setHasPermission] = useState<boolean | null>(null)
  const videoRef = useRef<HTMLVideoElement>(null)
  const streamRef = useRef<MediaStream | null>(null)

  useEffect(() => {
    return () => {
      // Cleanup on unmount
      stopScanning()
    }
  }, [])

  const startScanning = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { 
          facingMode: 'environment', // Use back camera if available
          width: { ideal: 640 },
          height: { ideal: 480 }
        } 
      })
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream
        streamRef.current = stream
        setIsScanning(true)
        setHasPermission(true)
        
        // Start QR code detection simulation
        simulateQRDetection()
      }
    } catch (error) {
      console.error('Error accessing camera:', error)
      setHasPermission(false)
      onScanError('Camera access denied or not available')
    }
  }

  const stopScanning = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop())
      streamRef.current = null
    }
    setIsScanning(false)
  }

  // Simulate QR code detection for demo purposes
  const simulateQRDetection = () => {
    // In a real implementation, you would use a QR code detection library
    // For demo, we'll simulate finding a QR code after a few seconds
    setTimeout(() => {
      if (isScanning) {
        // Simulate scanning a member's QR code
        const mockMemberIds = ['GYM001', 'GYM045', 'GYM089', 'GYM123', 'GYM156']
        const randomMemberId = mockMemberIds[Math.floor(Math.random() * mockMemberIds.length)]
        onScanSuccess(randomMemberId)
        stopScanning()
      }
    }, 3000)
  }

  if (hasPermission === false) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Camera className="h-5 w-5 mr-2" />
            QR Code Scanner
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <div className="text-muted-foreground">
            Akses kamera diperlukan untuk memindai kode QR
          </div>
          <Button onClick={startScanning}>
            <Camera className="h-4 w-4 mr-2" />
            Aktifkan Kamera
          </Button>
          <div className="text-sm text-muted-foreground">
            Atau gunakan pencarian manual
          </div>
          <Button variant="outline" onClick={onManualSearch}>
            <Search className="h-4 w-4 mr-2" />
            Pencarian Manual
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <Camera className="h-5 w-5 mr-2" />
            QR Code Scanner
          </div>
          {isScanning && (
            <Button variant="outline" size="sm" onClick={stopScanning}>
              <X className="h-4 w-4 mr-1" />
              Stop
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Camera View */}
        <div className="relative aspect-video bg-black rounded-lg overflow-hidden">
          {isScanning ? (
            <>
              <video
                ref={videoRef}
                autoPlay
                playsInline
                muted
                className="w-full h-full object-cover"
              />
              
              {/* Scanning Overlay */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-48 h-48 border-2 border-white border-dashed rounded-lg flex items-center justify-center">
                  <div className="text-white text-center">
                    <div className="text-2xl mb-2">📱</div>
                    <div className="text-sm">Posisikan kode QR di sini</div>
                  </div>
                </div>
              </div>

              {/* Scanning Animation */}
              <div className="absolute top-4 left-4 right-4">
                <div className="bg-black/50 text-white px-3 py-2 rounded-md text-sm text-center">
                  🔍 Memindai Kode QR...
                </div>
              </div>
            </>
          ) : (
            <div className="w-full h-full flex items-center justify-center text-white">
              <div className="text-center">
                <Camera className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <div className="text-lg mb-2">Kamera Siap</div>
                <div className="text-sm opacity-75">Klik mulai untuk memindai</div>
              </div>
            </div>
          )}
        </div>

        {/* Controls */}
        <div className="flex space-x-2">
          {!isScanning ? (
            <Button onClick={startScanning} className="flex-1">
              <Camera className="h-4 w-4 mr-2" />
              Mulai Pindai
            </Button>
          ) : (
            <Button onClick={stopScanning} variant="outline" className="flex-1">
              <X className="h-4 w-4 mr-2" />
              Berhenti Pindai
            </Button>
          )}

          <Button variant="outline" onClick={onManualSearch}>
            <Search className="h-4 w-4 mr-2" />
            Pencarian Manual
          </Button>
        </div>

        {/* Instructions */}
        <div className="text-sm text-muted-foreground text-center">
          <p>Arahkan kode QR anggota ke depan kamera</p>
          <p>Sistem akan otomatis mendeteksi dan memproses check-in</p>
        </div>
      </CardContent>
    </Card>
  )
}
