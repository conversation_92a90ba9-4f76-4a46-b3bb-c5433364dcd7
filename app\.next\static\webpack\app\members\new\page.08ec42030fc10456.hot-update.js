"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/members/new/page",{

/***/ "(app-pages-browser)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateExpiryDate: () => (/* binding */ calculateExpiryDate),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   generateMemberId: () => (/* binding */ generateMemberId),\n/* harmony export */   getDaysRemaining: () => (/* binding */ getDaysRemaining),\n/* harmony export */   getMembershipStatus: () => (/* binding */ getMembershipStatus),\n/* harmony export */   getPackageDisplayName: () => (/* binding */ getPackageDisplayName)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn() {\n    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){\n        inputs[_key] = arguments[_key];\n    }\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatCurrency(amount) {\n    return new Intl.NumberFormat('id-ID', {\n        style: 'currency',\n        currency: 'IDR',\n        minimumFractionDigits: 0\n    }).format(amount);\n}\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat('id-ID', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n    }).format(date);\n}\nfunction formatDateTime(date) {\n    return new Intl.DateTimeFormat('id-ID', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n    }).format(date);\n}\nfunction generateMemberId(count) {\n    return \"GYM\".concat(String(count + 1).padStart(3, '0'));\n}\nfunction calculateExpiryDate(startDate, packageType) {\n    const expiry = new Date(startDate);\n    switch(packageType){\n        case 'daily':\n            expiry.setDate(expiry.getDate() + 1);\n            break;\n        case 'weekly':\n            expiry.setDate(expiry.getDate() + 7);\n            break;\n        case 'monthly-basic':\n        case 'monthly-unlimited':\n            expiry.setMonth(expiry.getMonth() + 1);\n            break;\n        case 'quarterly':\n            expiry.setMonth(expiry.getMonth() + 3);\n            break;\n        default:\n            expiry.setMonth(expiry.getMonth() + 1);\n    }\n    return expiry;\n}\nfunction getDaysRemaining(expiryDate) {\n    const today = new Date();\n    const diffTime = expiryDate.getTime() - today.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays;\n}\nfunction getMembershipStatus(expiryDate) {\n    const daysRemaining = getDaysRemaining(expiryDate);\n    if (daysRemaining < 0) return 'expired';\n    if (daysRemaining <= 7) return 'expires-soon';\n    return 'active';\n}\nfunction getPackageDisplayName(packageType) {\n    switch(packageType){\n        case 'daily':\n            return 'Paket Harian';\n        case 'weekly':\n            return 'Paket Mingguan';\n        case 'monthly-basic':\n            return 'Paket Bulanan Basic';\n        case 'monthly-unlimited':\n            return 'Paket Bulanan Unlimited';\n        case 'quarterly':\n            return 'Paket Triwulan';\n        default:\n            return packageType;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/utils.ts\n"));

/***/ })

});