'use client'

import { useState, useMemo } from 'react'
import { useRouter } from 'next/navigation'
import { TrendingUp, Users } from 'lucide-react'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { MainLayout } from '@/components/layout/main-layout'
import { QRScanner } from '@/components/features/checkin/qr-scanner'
import { ManualSearch } from '@/components/features/checkin/manual-search'
import { CheckInSuccess } from '@/components/features/checkin/checkin-success'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/hooks/useAuth'
import { mockMembers, mockVisits } from '@/data/mockData'
import { Member } from '@/types'
import { formatDateTime, getMembershipStatus } from '@/lib/utils'

type CheckInMode = 'scanner' | 'manual' | 'success'

export default function CheckInPage() {
  const { user } = useAuth()
  const router = useRouter()
  const [mode, setMode] = useState<CheckInMode>('scanner')
  const [checkedInMember, setCheckedInMember] = useState<Member | null>(null)
  const [checkInTime, setCheckInTime] = useState<Date>(new Date())

  // Calculate current occupancy and recent check-ins
  const { currentOccupancy, recentCheckIns } = useMemo(() => {
    const today = new Date()
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate())
    
    const todayVisits = mockVisits.filter(visit => visit.checkInTime >= todayStart)
    const currentOccupancy = Math.min(todayVisits.length + 1, 30) // +1 for potential new check-in
    
    const recentCheckIns = mockVisits
      .sort((a, b) => b.checkInTime.getTime() - a.checkInTime.getTime())
      .slice(0, 5)
    
    return { currentOccupancy, recentCheckIns }
  }, [])

  const maxCapacity = 30

  const handleQRScanSuccess = (memberId: string) => {
    const member = mockMembers.find(m => m.memberId === memberId)
    if (member) {
      const status = getMembershipStatus(member.expiryDate)
      if (status === 'expired') {
        alert(`Member ${member.fullName} has an expired membership. Please renew before check-in.`)
        return
      }
      
      setCheckedInMember(member)
      setCheckInTime(new Date())
      setMode('success')
    } else {
      alert('Member not found. Please try again or use manual search.')
    }
  }

  const handleQRScanError = (error: string) => {
    console.error('QR Scan Error:', error)
    alert(`Scanner error: ${error}`)
  }

  const handleManualCheckIn = (member: Member) => {
    const status = getMembershipStatus(member.expiryDate)
    if (status === 'expired') {
      alert(`Member ${member.fullName} has an expired membership. Please renew before check-in.`)
      return
    }
    
    setCheckedInMember(member)
    setCheckInTime(new Date())
    setMode('success')
  }

  const handleRenewMembership = (member: Member) => {
    // TODO: Navigate to payment/renewal page
    router.push(`/payments/new?memberId=${member.id}&type=renewal`)
  }

  const handleNewCheckIn = () => {
    setCheckedInMember(null)
    setMode('scanner')
  }

  const handleBackToDashboard = () => {
    router.push('/dashboard')
  }

  const getOccupancyStatus = () => {
    const percentage = Math.round((currentOccupancy / maxCapacity) * 100)
    if (percentage >= 90) return { label: '🔴 Near Capacity', variant: 'destructive' as const }
    if (percentage >= 70) return { label: '🟡 Moderate', variant: 'warning' as const }
    return { label: '🟢 Normal Capacity', variant: 'success' as const }
  }

  const occupancyStatus = getOccupancyStatus()

  if (mode === 'success' && checkedInMember) {
    return (
      <ProtectedRoute>
        <MainLayout user={user}>
          <CheckInSuccess
            member={checkedInMember}
            checkInTime={checkInTime}
            currentOccupancy={currentOccupancy}
            maxCapacity={maxCapacity}
            onNewCheckIn={handleNewCheckIn}
            onBackToDashboard={handleBackToDashboard}
          />
        </MainLayout>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <MainLayout user={user}>
        <div className="space-y-6">
          {/* Header */}
          <div className="text-center">
            <h1 className="text-3xl font-bold mb-2">📱 Check-in Anggota</h1>
            <p className="text-muted-foreground">Proses check-in cepat 30 detik</p>
          </div>

          <div className="grid gap-6 lg:grid-cols-3">
            {/* Main Check-in Area */}
            <div className="lg:col-span-2">
              {mode === 'scanner' && (
                <QRScanner
                  onScanSuccess={handleQRScanSuccess}
                  onScanError={handleQRScanError}
                  onManualSearch={() => setMode('manual')}
                />
              )}
              
              {mode === 'manual' && (
                <ManualSearch
                  onBack={() => setMode('scanner')}
                  onCheckIn={handleManualCheckIn}
                  onRenew={handleRenewMembership}
                />
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Current Occupancy */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <Users className="h-5 w-5 mr-2" />
                    Okupansi Saat Ini
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center space-y-4">
                    <div>
                      <div className="text-3xl font-bold">
                        {currentOccupancy} / {maxCapacity}
                      </div>
                      <div className="text-sm text-muted-foreground">Anggota</div>
                    </div>

                    {/* Progress Bar */}
                    <div className="space-y-2">
                      <div className="w-full bg-gray-200 rounded-full h-3">
                        <div 
                          className={`h-3 rounded-full transition-all duration-500 ${
                            Math.round((currentOccupancy / maxCapacity) * 100) >= 90 ? 'bg-red-500' :
                            Math.round((currentOccupancy / maxCapacity) * 100) >= 70 ? 'bg-yellow-500' : 'bg-green-500'
                          }`}
                          style={{ width: `${Math.round((currentOccupancy / maxCapacity) * 100)}%` }}
                        />
                      </div>
                      <div className="text-center">
                        <Badge variant={occupancyStatus.variant}>
                          {occupancyStatus.label}
                        </Badge>
                      </div>
                    </div>

                    <div className="text-sm text-muted-foreground">
                      <p>Jam Sibuk:</p>
                      <p>• 06:00-08:00</p>
                      <p>• 18:00-20:00</p>
                    </div>

                    <Button variant="outline" size="sm" className="w-full">
                      <TrendingUp className="h-4 w-4 mr-2" />
                      Lihat Analitik
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Recent Check-ins */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Check-in Terbaru (Hari Ini)</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {recentCheckIns.length === 0 ? (
                      <p className="text-sm text-muted-foreground text-center py-4">
                        Belum ada check-in hari ini
                      </p>
                    ) : (
                      recentCheckIns.map((visit) => (
                        <div key={visit.id} className="flex items-center space-x-3 text-sm">
                          <div className="w-2 h-2 bg-green-500 rounded-full flex-shrink-0" />
                          <div className="flex-1 min-w-0">
                            <div className="font-medium truncate">
                              {visit.memberName}
                            </div>
                            <div className="text-muted-foreground">
                              {formatDateTime(visit.checkInTime)}
                            </div>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                  
                  {recentCheckIns.length > 0 && (
                    <div className="mt-4 text-center">
                      <Button variant="outline" size="sm">
                        Lihat Semua Check-in
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </MainLayout>
    </ProtectedRoute>
  )
}
