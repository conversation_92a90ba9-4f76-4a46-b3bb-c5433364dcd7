'use client'

import { useEffect, useState } from 'react'
import { CheckCircle, User, Calendar, Clock, Users, ArrowLeft } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Member } from '@/types'
import { formatDateTime, formatDate } from '@/lib/utils'

interface CheckInSuccessProps {
  member: Member
  checkInTime: Date
  currentOccupancy: number
  maxCapacity: number
  onNewCheckIn: () => void
  onBackToDashboard: () => void
}

export function CheckInSuccess({ 
  member, 
  checkInTime, 
  currentOccupancy, 
  maxCapacity,
  onNewCheckIn,
  onBackToDashboard 
}: CheckInSuccessProps) {
  const [countdown, setCountdown] = useState(5)

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          clearInterval(timer)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const occupancyPercentage = Math.round((currentOccupancy / maxCapacity) * 100)
  
  const getOccupancyStatus = () => {
    if (occupancyPercentage >= 90) return { label: 'Near Capacity', color: 'text-red-600', bg: 'bg-red-100' }
    if (occupancyPercentage >= 70) return { label: 'Moderate', color: 'text-yellow-600', bg: 'bg-yellow-100' }
    return { label: 'Normal', color: 'text-green-600', bg: 'bg-green-100' }
  }

  const occupancyStatus = getOccupancyStatus()

  const getPackageDisplayName = (packageType: string) => {
    switch (packageType) {
      case 'daily':
        return 'Daily Pass'
      case 'weekly':
        return 'Weekly Pass'
      case 'monthly-basic':
        return 'Monthly Basic'
      case 'monthly-unlimited':
        return 'Monthly Unlimited'
      case 'quarterly':
        return 'Quarterly'
      default:
        return packageType
    }
  }

  return (
    <div className="space-y-6">
      {/* Success Header */}
      <Card className="border-green-200 bg-green-50">
        <CardContent className="p-6 text-center">
          <div className="space-y-4">
            <div className="flex justify-center">
              <CheckCircle className="h-16 w-16 text-green-600" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-green-800 mb-2">
                ✅ Check-in Berhasil!
              </h2>
              <p className="text-green-700">
                Selamat datang di StrongFit Gym
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Member Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <User className="h-5 w-5 mr-2" />
            Detail Anggota
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-start space-x-4">
            {/* Avatar */}
            <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center flex-shrink-0">
              {member.photoURL ? (
                <img 
                  src={member.photoURL} 
                  alt={member.fullName}
                  className="w-16 h-16 rounded-full object-cover"
                />
              ) : (
                <span className="text-2xl">📷</span>
              )}
            </div>

            {/* Details */}
            <div className="flex-1 space-y-2">
              <div>
                <div className="flex items-center space-x-2 mb-1">
                  <span className="text-sm font-medium text-muted-foreground">
                    {member.memberId}
                  </span>
                  <Badge variant="success">Aktif</Badge>
                </div>
                <h3 className="text-xl font-semibold">{member.fullName}</h3>
                <p className="text-muted-foreground">📱 {member.phoneNumber}</p>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Paket:</span>
                  <div>{getPackageDisplayName(member.packageType)}</div>
                </div>
                <div>
                  <span className="font-medium">Berakhir:</span>
                  <div>{formatDate(member.expiryDate)}</div>
                </div>
                <div>
                  <span className="font-medium">Total Kunjungan:</span>
                  <div>{member.totalVisits + 1}</div>
                </div>
                <div>
                  <span className="font-medium">Waktu Check-in:</span>
                  <div>{formatDateTime(checkInTime)}</div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Current Occupancy */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="h-5 w-5 mr-2" />
            Current Occupancy
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="text-center">
              <div className="text-3xl font-bold">
                {currentOccupancy} / {maxCapacity}
              </div>
              <div className="text-muted-foreground">Members in gym</div>
            </div>

            {/* Progress Bar */}
            <div className="space-y-2">
              <div className="w-full bg-gray-200 rounded-full h-4">
                <div 
                  className={`h-4 rounded-full transition-all duration-500 ${
                    occupancyPercentage >= 90 ? 'bg-red-500' :
                    occupancyPercentage >= 70 ? 'bg-yellow-500' : 'bg-green-500'
                  }`}
                  style={{ width: `${occupancyPercentage}%` }}
                />
              </div>
              <div className="flex justify-between text-sm">
                <span>{occupancyPercentage}% Full</span>
                <span className={`px-2 py-1 rounded-full text-xs ${occupancyStatus.bg} ${occupancyStatus.color}`}>
                  {occupancyStatus.label} Capacity
                </span>
              </div>
            </div>

            {/* Peak Hours Info */}
            <div className="text-sm text-muted-foreground text-center p-3 bg-muted/50 rounded-md">
              <Clock className="h-4 w-4 inline mr-1" />
              Peak Hours: 6:00-8:00 AM, 6:00-8:00 PM
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex space-x-4">
        <Button 
          onClick={onNewCheckIn}
          className="flex-1 h-12"
          size="lg"
        >
          📱 New Check-in
        </Button>
        <Button 
          variant="outline"
          onClick={onBackToDashboard}
          className="flex-1 h-12"
          size="lg"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Dashboard
        </Button>
      </div>

      {/* Auto-redirect countdown */}
      {countdown > 0 && (
        <div className="text-center text-sm text-muted-foreground">
          Automatically returning to check-in in {countdown} seconds...
        </div>
      )}
    </div>
  )
}
