'use client'

import { useMemo } from 'react'
import { Users, CreditCard, Smartphone, TrendingUp, UserPlus } from 'lucide-react'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { MainLayout } from '@/components/layout/main-layout'
import { StatsCard } from '@/components/features/dashboard/stats-card'
import { RecentActivities, createActivitiesFromData } from '@/components/features/dashboard/recent-activities'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/hooks/useAuth'
import { mockMembers, mockPayments, mockVisits } from '@/data/mockData'
import { formatCurrency, getMembershipStatus, getDaysRemaining } from '@/lib/utils'

export default function DashboardPage() {
  const { user } = useAuth()

  // Calculate dashboard statistics
  const stats = useMemo(() => {
    const today = new Date()
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate())
    const yesterday = new Date(todayStart.getTime() - 24 * 60 * 60 * 1000)

    // Today's visitors
    const todayVisits = mockVisits.filter(visit => 
      visit.checkInTime >= todayStart
    )
    const yesterdayVisits = mockVisits.filter(visit => 
      visit.checkInTime >= yesterday && visit.checkInTime < todayStart
    )
    const todayVisitors = todayVisits.length
    const visitorsTrend = yesterdayVisits.length > 0 
      ? Math.round(((todayVisitors - yesterdayVisits.length) / yesterdayVisits.length) * 100)
      : 0

    // Today's revenue
    const todayPayments = mockPayments.filter(payment => 
      payment.paymentDate >= todayStart
    )
    const yesterdayPayments = mockPayments.filter(payment => 
      payment.paymentDate >= yesterday && payment.paymentDate < todayStart
    )
    const todayRevenue = todayPayments.reduce((sum, payment) => sum + payment.amount, 0)
    const yesterdayRevenue = yesterdayPayments.reduce((sum, payment) => sum + payment.amount, 0)
    const revenueTrend = yesterdayRevenue > 0 
      ? Math.round(((todayRevenue - yesterdayRevenue) / yesterdayRevenue) * 100)
      : 0

    // Current occupancy (members who checked in today and haven't left)
    const currentOccupancy = Math.min(todayVisitors, 30) // Assuming max capacity of 30
    const maxCapacity = 30
    const occupancyPercentage = Math.round((currentOccupancy / maxCapacity) * 100)

    // Members expiring this week
    const oneWeekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000)
    const expiringThisWeek = mockMembers.filter(member => 
      member.expiryDate <= oneWeekFromNow && member.expiryDate > today
    ).length

    // Active members
    const activeMembers = mockMembers.filter(member => 
      getMembershipStatus(member.expiryDate) === 'active'
    ).length

    // New members this week
    const oneWeekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
    const newMembersThisWeek = mockMembers.filter(member => 
      member.createdAt >= oneWeekAgo
    ).length

    return {
      todayVisitors,
      visitorsTrend,
      todayRevenue,
      revenueTrend,
      currentOccupancy,
      maxCapacity,
      occupancyPercentage,
      expiringThisWeek,
      activeMembers,
      newMembersThisWeek,
    }
  }, [])

  // Create recent activities
  const recentActivities = useMemo(() => {
    return createActivitiesFromData(mockVisits, mockPayments)
  }, [])

  const getOccupancyStatus = () => {
    if (stats.occupancyPercentage >= 90) return { label: 'Hampir Penuh', variant: 'destructive' as const }
    if (stats.occupancyPercentage >= 70) return { label: 'Sedang', variant: 'warning' as const }
    return { label: 'Kapasitas Normal', variant: 'success' as const }
  }

  return (
    <ProtectedRoute>
      <MainLayout user={user}>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">Dashboard</h1>
              <p className="text-muted-foreground">Ringkasan Hari Ini</p>
            </div>
            <div className="text-right">
              <p className="text-sm text-muted-foreground">
                {new Date().toLocaleDateString('id-ID', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </p>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
            <StatsCard
              title="Pengunjung Hari Ini"
              value={stats.todayVisitors}
              subtitle="pengunjung"
              trend={{
                value: stats.visitorsTrend,
                label: `${stats.visitorsTrend > 0 ? '+' : ''}${stats.visitorsTrend}% vs kemarin`,
                type: stats.visitorsTrend > 0 ? 'up' : stats.visitorsTrend < 0 ? 'down' : 'neutral'
              }}
              icon={<Smartphone className="h-4 w-4" />}
            />

            <StatsCard
              title="Pendapatan Hari Ini"
              value={stats.todayRevenue}
              subtitle="diperoleh"
              trend={{
                value: stats.revenueTrend,
                label: `${stats.revenueTrend > 0 ? '+' : ''}${stats.revenueTrend}% vs kemarin`,
                type: stats.revenueTrend > 0 ? 'up' : stats.revenueTrend < 0 ? 'down' : 'neutral'
              }}
              icon={<CreditCard className="h-4 w-4" />}
            />

            <StatsCard
              title="Okupansi Saat Ini"
              value={`${stats.currentOccupancy}/${stats.maxCapacity}`}
              subtitle="anggota"
              status={getOccupancyStatus()}
              icon={<Users className="h-4 w-4" />}
            />

            <StatsCard
              title="Berakhir Minggu Ini"
              value={stats.expiringThisWeek}
              subtitle="anggota"
              status={stats.expiringThisWeek > 0 ? { label: 'Perlu Tindakan', variant: 'warning' } : undefined}
              icon={<TrendingUp className="h-4 w-4" />}
            />

            <StatsCard
              title="Anggota Aktif"
              value={stats.activeMembers}
              subtitle="anggota"
              trend={{
                value: stats.newMembersThisWeek,
                label: `+${stats.newMembersThisWeek} baru minggu ini`,
                type: 'up'
              }}
              icon={<UserPlus className="h-4 w-4" />}
            />
          </div>

          {/* Main Content */}
          <div className="grid gap-6 lg:grid-cols-2">
            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Aksi Cepat</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full justify-start h-12" size="lg">
                  <Smartphone className="mr-3 h-5 w-5" />
                  Check-in Cepat
                </Button>
                <Button className="w-full justify-start h-12" variant="outline" size="lg">
                  <UserPlus className="mr-3 h-5 w-5" />
                  Tambah Anggota Baru
                </Button>
                <Button className="w-full justify-start h-12" variant="outline" size="lg">
                  <CreditCard className="mr-3 h-5 w-5" />
                  Catat Pembayaran
                </Button>
              </CardContent>
            </Card>

            {/* Recent Activities */}
            <RecentActivities activities={recentActivities} />
          </div>
        </div>
      </MainLayout>
    </ProtectedRoute>
  )
}
